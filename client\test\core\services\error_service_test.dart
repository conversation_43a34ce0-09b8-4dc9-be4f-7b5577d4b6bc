import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';

import '../../../lib/core/services/error_service.dart';

void main() {
  group('ErrorService', () {
    late ErrorService errorService;

    setUp(() {
      errorService = ErrorService();
      errorService.configure(
        showErrorSnackbars: true,
        logErrors: true,
        reportErrors: true,
      );
    });

    tearDown(() {
      errorService.clearHistory();
    });

    group('handleError', () {
      test('should add error to history', () {
        // Arrange
        const error = 'Test error';
        const context = 'Test context';

        // Act
        errorService.handleError(
          error,
          context: context,
          severity: ErrorSeverity.medium,
        );

        // Assert
        expect(errorService.errorHistory.length, equals(1));
        expect(errorService.errorHistory.first.error, equals(error));
        expect(errorService.errorHistory.first.context, equals(context));
        expect(errorService.errorHistory.first.severity, equals(ErrorSeverity.medium));
      });

      test('should emit error event', () async {
        // Arrange
        const error = 'Test error';
        AppError? emittedError;
        
        errorService.errorStream.listen((appError) {
          emittedError = appError;
        });

        // Act
        errorService.handleError(error);

        // Wait for stream event
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        expect(emittedError, isNotNull);
        expect(emittedError!.error, equals(error));
      });

      test('should limit error history to 100 entries', () {
        // Act - Add 150 errors
        for (int i = 0; i < 150; i++) {
          errorService.handleError('Error $i');
        }

        // Assert
        expect(errorService.errorHistory.length, equals(100));
        expect(errorService.errorHistory.first.error, equals('Error 50')); // First 50 should be removed
        expect(errorService.errorHistory.last.error, equals('Error 149'));
      });
    });

    group('handleApiError', () {
      test('should handle 400 status code correctly', () {
        // Act
        errorService.handleApiError(
          'Bad request',
          statusCode: 400,
          endpoint: '/api/test',
          method: 'POST',
        );

        // Assert
        final error = errorService.errorHistory.first;
        expect(error.severity, equals(ErrorSeverity.low));
        expect(error.userMessage, contains('Invalid request'));
        expect(error.metadata?['status_code'], equals(400));
        expect(error.metadata?['endpoint'], equals('/api/test'));
      });

      test('should handle 401 status code correctly', () {
        // Act
        errorService.handleApiError('Unauthorized', statusCode: 401);

        // Assert
        final error = errorService.errorHistory.first;
        expect(error.severity, equals(ErrorSeverity.medium));
        expect(error.userMessage, contains('Authentication required'));
      });

      test('should handle 500 status code correctly', () {
        // Act
        errorService.handleApiError('Server error', statusCode: 500);

        // Assert
        final error = errorService.errorHistory.first;
        expect(error.severity, equals(ErrorSeverity.high));
        expect(error.userMessage, contains('Server error'));
      });

      test('should handle unknown status code correctly', () {
        // Act
        errorService.handleApiError('Unknown error', statusCode: 999);

        // Assert
        final error = errorService.errorHistory.first;
        expect(error.severity, equals(ErrorSeverity.medium));
        expect(error.userMessage, equals('Unknown error'));
      });
    });

    group('handleNetworkError', () {
      test('should set high severity and appropriate message', () {
        // Act
        errorService.handleNetworkError('Connection timeout');

        // Assert
        final error = errorService.errorHistory.first;
        expect(error.severity, equals(ErrorSeverity.high));
        expect(error.userMessage, contains('Network connection error'));
        expect(error.context, equals('Network Error'));
      });
    });

    group('handleValidationError', () {
      test('should set low severity and include field errors', () {
        // Arrange
        const message = 'Validation failed';
        final fieldErrors = {
          'email': ['Email is required', 'Email format is invalid'],
          'password': ['Password is too short'],
        };

        // Act
        errorService.handleValidationError(
          message,
          fieldErrors: fieldErrors,
        );

        // Assert
        final error = errorService.errorHistory.first;
        expect(error.severity, equals(ErrorSeverity.low));
        expect(error.userMessage, equals(message));
        expect(error.context, equals('Validation Error'));
        expect(error.metadata?['field_errors'], equals(fieldErrors));
      });
    });

    group('clearHistory', () {
      test('should remove all errors from history', () {
        // Arrange - Add some errors
        errorService.handleError('Error 1');
        errorService.handleError('Error 2');
        errorService.handleError('Error 3');

        expect(errorService.errorHistory.length, equals(3));

        // Act
        errorService.clearHistory();

        // Assert
        expect(errorService.errorHistory.isEmpty, isTrue);
      });
    });

    group('configuration', () {
      test('should update configuration correctly', () {
        // Act
        errorService.configure(
          showErrorSnackbars: false,
          logErrors: false,
          reportErrors: false,
        );

        // The configuration is private, so we can't directly test it
        // But we can verify it doesn't throw errors
        errorService.handleError('Test error');
        
        // Assert - Should still add to history
        expect(errorService.errorHistory.length, equals(1));
      });
    });
  });

  group('AppError', () {
    test('should create correctly with all properties', () {
      // Arrange
      const error = 'Test error';
      final stackTrace = StackTrace.current;
      const context = 'Test context';
      const severity = ErrorSeverity.high;
      const userMessage = 'User friendly message';
      final metadata = {'key': 'value'};
      final timestamp = DateTime.now();

      // Act
      final appError = AppError(
        error: error,
        stackTrace: stackTrace,
        context: context,
        severity: severity,
        userMessage: userMessage,
        metadata: metadata,
        timestamp: timestamp,
      );

      // Assert
      expect(appError.error, equals(error));
      expect(appError.stackTrace, equals(stackTrace));
      expect(appError.context, equals(context));
      expect(appError.severity, equals(severity));
      expect(appError.userMessage, equals(userMessage));
      expect(appError.metadata, equals(metadata));
      expect(appError.timestamp, equals(timestamp));
    });

    test('should have meaningful toString', () {
      // Arrange
      final timestamp = DateTime.now();
      final appError = AppError(
        error: 'Test error',
        context: 'Test context',
        severity: ErrorSeverity.medium,
        timestamp: timestamp,
      );

      // Act
      final string = appError.toString();

      // Assert
      expect(string, contains('Test error'));
      expect(string, contains('Test context'));
      expect(string, contains('medium'));
      expect(string, contains(timestamp.toString()));
    });
  });

  group('ErrorSeverity', () {
    test('should have all expected values', () {
      expect(ErrorSeverity.values.length, equals(4));
      expect(ErrorSeverity.values, contains(ErrorSeverity.low));
      expect(ErrorSeverity.values, contains(ErrorSeverity.medium));
      expect(ErrorSeverity.values, contains(ErrorSeverity.high));
      expect(ErrorSeverity.values, contains(ErrorSeverity.critical));
    });
  });

  group('Widget Tests', () {
    testWidgets('showErrorDialog should display error information', (WidgetTester tester) async {
      // Arrange
      final appError = AppError(
        error: 'Test error',
        context: 'Test context',
        severity: ErrorSeverity.medium,
        userMessage: 'User friendly message',
        timestamp: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    errorService.showErrorDialog(context, appError);
                  },
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.text('Show Error'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Error'), findsOneWidget);
      expect(find.text('User friendly message'), findsOneWidget);
      expect(find.text('Context: Test context'), findsOneWidget);
      expect(find.text('OK'), findsOneWidget);
    });

    testWidgets('showErrorSnackbar should display snackbar', (WidgetTester tester) async {
      // Arrange
      final appError = AppError(
        error: 'Test error',
        severity: ErrorSeverity.low,
        userMessage: 'User friendly message',
        timestamp: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    errorService.showErrorSnackbar(context, appError);
                  },
                  child: const Text('Show Snackbar'),
                );
              },
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.text('Show Snackbar'));
      await tester.pump();

      // Assert
      expect(find.text('User friendly message'), findsOneWidget);
      expect(find.byType(SnackBar), findsOneWidget);
    });
  });
}
