name: server
description: Dart HTTP server with comprehensive gamification APIs for Quester platform
version: 1.0.0
publish_to: none

environment:
  sdk: ^3.8.1

dependencies:
  shelf: ^1.4.0
  shelf_router: ^1.1.0
  
  # Database connections
  postgres: ^3.5.6
  
  # WebSocket support
  web_socket_channel: ^2.4.0
  
  # XML processing for SAML
  xml: ^6.4.2
  
  # Internationalization for date formatting
  intl: ^0.19.0
  
  # Shared models, DTOs, and utilities
  shared:
    path: ../shared
    
  # HTTP utilities
  shelf_cors_headers: ^0.1.5
  
  # WebSocket support for real-time features
  shelf_web_socket: ^2.0.0
  
  # JSON handling
  json_annotation: ^4.9.0
  
  # Utilities
  uuid: ^4.4.0
  
  # Authentication
  crypto: ^3.0.3
  
  # Environment configuration
  dotenv: ^4.2.0
  
  # Redis for session management
  redis: ^4.0.0

dev_dependencies:
  http: ^1.1.0
  lints: ^5.0.0
  test: ^1.24.0
  
  # Code generation
  build_runner: ^2.4.12
  json_serializable: ^6.8.0
