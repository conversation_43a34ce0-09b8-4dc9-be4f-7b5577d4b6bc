import 'dart:async';
import 'package:flutter/material.dart';

/// Loading state management service for the Quester application
/// Provides centralized loading state management and UI feedback
class LoadingService {
  static final LoadingService _instance = LoadingService._internal();
  factory LoadingService() => _instance;
  LoadingService._internal();

  // Loading state tracking
  final Map<String, LoadingState> _loadingStates = {};
  final StreamController<Map<String, LoadingState>> _loadingController = 
      StreamController<Map<String, LoadingState>>.broadcast();
  
  // Global loading overlay
  OverlayEntry? _globalOverlay;
  BuildContext? _overlayContext;

  /// Stream of loading states
  Stream<Map<String, LoadingState>> get loadingStream => _loadingController.stream;
  
  /// Get current loading states
  Map<String, LoadingState> get loadingStates => Map.unmodifiable(_loadingStates);
  
  /// Check if any operation is loading
  bool get isAnyLoading => _loadingStates.values.any((state) => state.isLoading);
  
  /// Check if specific operation is loading
  bool isLoading(String key) => _loadingStates[key]?.isLoading ?? false;
  
  /// Get loading state for specific operation
  LoadingState? getLoadingState(String key) => _loadingStates[key];

  /// Start loading for a specific operation
  void startLoading(
    String key, {
    String? message,
    bool showGlobalOverlay = false,
    BuildContext? context,
  }) {
    _loadingStates[key] = LoadingState(
      key: key,
      isLoading: true,
      message: message,
      startTime: DateTime.now(),
    );
    
    _loadingController.add(_loadingStates);
    
    if (showGlobalOverlay && context != null) {
      _showGlobalOverlay(context, message);
    }
  }

  /// Stop loading for a specific operation
  void stopLoading(String key) {
    final currentState = _loadingStates[key];
    if (currentState != null) {
      _loadingStates[key] = currentState.copyWith(
        isLoading: false,
        endTime: DateTime.now(),
      );
      
      _loadingController.add(_loadingStates);
      
      // Hide global overlay if this was the last loading operation
      if (!isAnyLoading) {
        _hideGlobalOverlay();
      }
    }
  }

  /// Update loading message
  void updateLoadingMessage(String key, String message) {
    final currentState = _loadingStates[key];
    if (currentState != null && currentState.isLoading) {
      _loadingStates[key] = currentState.copyWith(message: message);
      _loadingController.add(_loadingStates);
    }
  }

  /// Execute operation with loading state management
  Future<T> withLoading<T>(
    String key,
    Future<T> Function() operation, {
    String? message,
    bool showGlobalOverlay = false,
    BuildContext? context,
    Function(dynamic error)? onError,
  }) async {
    startLoading(key, message: message, showGlobalOverlay: showGlobalOverlay, context: context);
    
    try {
      final result = await operation();
      stopLoading(key);
      return result;
    } catch (error) {
      stopLoading(key);
      if (onError != null) {
        onError(error);
      }
      rethrow;
    }
  }

  /// Show global loading overlay
  void _showGlobalOverlay(BuildContext context, String? message) {
    if (_globalOverlay != null) return;
    
    _overlayContext = context;
    _globalOverlay = OverlayEntry(
      builder: (context) => GlobalLoadingOverlay(message: message),
    );
    
    Overlay.of(context).insert(_globalOverlay!);
  }

  /// Hide global loading overlay
  void _hideGlobalOverlay() {
    _globalOverlay?.remove();
    _globalOverlay = null;
    _overlayContext = null;
  }

  /// Clear all loading states
  void clearAll() {
    _loadingStates.clear();
    _loadingController.add(_loadingStates);
    _hideGlobalOverlay();
  }

  /// Clear specific loading state
  void clear(String key) {
    _loadingStates.remove(key);
    _loadingController.add(_loadingStates);
  }

  /// Dispose resources
  void dispose() {
    _loadingController.close();
    _hideGlobalOverlay();
  }
}

/// Loading state model
class LoadingState {
  final String key;
  final bool isLoading;
  final String? message;
  final DateTime startTime;
  final DateTime? endTime;

  const LoadingState({
    required this.key,
    required this.isLoading,
    this.message,
    required this.startTime,
    this.endTime,
  });

  /// Get loading duration
  Duration get duration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  /// Copy with new values
  LoadingState copyWith({
    String? key,
    bool? isLoading,
    String? message,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    return LoadingState(
      key: key ?? this.key,
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  @override
  String toString() {
    return 'LoadingState(key: $key, isLoading: $isLoading, message: $message, duration: ${duration.inMilliseconds}ms)';
  }
}

/// Global loading overlay widget
class GlobalLoadingOverlay extends StatelessWidget {
  final String? message;

  const GlobalLoadingOverlay({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              if (message != null) ...[
                const SizedBox(height: 16),
                Text(
                  message!,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Loading indicator widget for specific operations
class LoadingIndicator extends StatelessWidget {
  final String loadingKey;
  final Widget child;
  final Widget? loadingWidget;
  final bool showMessage;

  const LoadingIndicator({
    super.key,
    required this.loadingKey,
    required this.child,
    this.loadingWidget,
    this.showMessage = true,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Map<String, LoadingState>>(
      stream: LoadingService().loadingStream,
      builder: (context, snapshot) {
        final loadingStates = snapshot.data ?? {};
        final loadingState = loadingStates[loadingKey];
        final isLoading = loadingState?.isLoading ?? false;

        if (isLoading) {
          return loadingWidget ?? _buildDefaultLoadingWidget(loadingState);
        }

        return child;
      },
    );
  }

  Widget _buildDefaultLoadingWidget(LoadingState? loadingState) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          if (showMessage && loadingState?.message != null) ...[
            const SizedBox(height: 16),
            Text(
              loadingState!.message!,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Loading button widget
class LoadingButton extends StatelessWidget {
  final String loadingKey;
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;
  final String? loadingText;

  const LoadingButton({
    super.key,
    required this.loadingKey,
    required this.onPressed,
    required this.child,
    this.style,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Map<String, LoadingState>>(
      stream: LoadingService().loadingStream,
      builder: (context, snapshot) {
        final loadingStates = snapshot.data ?? {};
        final isLoading = loadingStates[loadingKey]?.isLoading ?? false;

        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: style,
          child: isLoading
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    if (loadingText != null) ...[
                      const SizedBox(width: 8),
                      Text(loadingText!),
                    ],
                  ],
                )
              : child,
        );
      },
    );
  }
}

/// Loading mixin for widgets
mixin LoadingMixin<T extends StatefulWidget> on State<T> {
  final LoadingService _loadingService = LoadingService();

  /// Start loading with automatic key generation
  void startLoading({String? message, bool showGlobalOverlay = false}) {
    final key = '${widget.runtimeType}_${hashCode}';
    _loadingService.startLoading(
      key,
      message: message,
      showGlobalOverlay: showGlobalOverlay,
      context: context,
    );
  }

  /// Stop loading with automatic key generation
  void stopLoading() {
    final key = '${widget.runtimeType}_${hashCode}';
    _loadingService.stopLoading(key);
  }

  /// Check if this widget is loading
  bool get isLoading {
    final key = '${widget.runtimeType}_${hashCode}';
    return _loadingService.isLoading(key);
  }

  /// Execute operation with loading state
  Future<T> withLoading<T>(
    Future<T> Function() operation, {
    String? message,
    bool showGlobalOverlay = false,
  }) async {
    final key = '${widget.runtimeType}_${hashCode}';
    return _loadingService.withLoading(
      key,
      operation,
      message: message,
      showGlobalOverlay: showGlobalOverlay,
      context: context,
    );
  }

  @override
  void dispose() {
    stopLoading();
    super.dispose();
  }
}
