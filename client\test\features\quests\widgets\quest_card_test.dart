import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quester_client/features/quests/widgets/quest_card.dart';
import 'package:shared/shared.dart';

void main() {
  group('QuestCard Widget Tests', () {
    late Quest mockQuest;

    setUp(() {
      mockQuest = Quest(
        id: 'quest_123',
        title: 'Test Quest',
        description: 'This is a test quest description',
        createdById: 'user_123',
        status: QuestStatus.active,
        priority: QuestPriority.high,
        difficulty: QuestDifficulty.intermediate,
        category: QuestCategory.personal,
        basePoints: 100,
        bonusPoints: 50,
        totalPoints: 150,
        earnedPoints: 75,
        progressPercentage: 50.0,
        taskIds: ['task_1', 'task_2'],
        participantIds: ['user_123'],
        tags: ['test', 'flutter'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    testWidgets('displays quest information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(quest: mockQuest),
          ),
        ),
      );

      // Verify quest title is displayed
      expect(find.text('Test Quest'), findsOneWidget);
      
      // Verify quest description is displayed
      expect(find.text('This is a test quest description'), findsOneWidget);
      
      // Verify quest points are displayed
      expect(find.textContaining('150'), findsOneWidget); // Total points
      
      // Verify progress is displayed
      expect(find.textContaining('50%'), findsOneWidget);
    });

    testWidgets('displays quest status correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(quest: mockQuest),
          ),
        ),
      );

      // Verify status is displayed (case-insensitive search)
      expect(find.textContaining('ACTIVE'), findsOneWidget);
    });

    testWidgets('displays quest priority correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(quest: mockQuest),
          ),
        ),
      );

      // Verify priority is displayed
      expect(find.textContaining('HIGH'), findsOneWidget);
    });

    testWidgets('displays quest difficulty correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(quest: mockQuest),
          ),
        ),
      );

      // Verify difficulty is displayed
      expect(find.textContaining('INTERMEDIATE'), findsOneWidget);
    });

    testWidgets('displays quest tags', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(quest: mockQuest),
          ),
        ),
      );

      // Verify tags are displayed
      expect(find.text('test'), findsOneWidget);
      expect(find.text('flutter'), findsOneWidget);
    });

    testWidgets('handles tap events', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(
              quest: mockQuest,
              onTap: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      // Tap the quest card
      await tester.tap(find.byType(QuestCard));
      await tester.pump();

      // Verify tap was handled
      expect(tapped, isTrue);
    });

    testWidgets('shows progress indicator', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(quest: mockQuest),
          ),
        ),
      );

      // Verify progress indicator is present
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('displays correct colors for different priorities', (WidgetTester tester) async {
      final highPriorityQuest = mockQuest.copyWith(priority: QuestPriority.high);
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuestCard(quest: highPriorityQuest),
          ),
        ),
      );

      // Find the card widget
      final cardFinder = find.byType(Card);
      expect(cardFinder, findsOneWidget);

      // Verify the card exists (color testing would require more complex setup)
      final card = tester.widget<Card>(cardFinder);
      expect(card, isNotNull);
    });
  });
}
