import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:http/http.dart' as http;
import 'package:shared/shared.dart';

import '../../../lib/core/services/search_service.dart';
import '../../../lib/core/services/api_service.dart' as api_service;

class MockApiService extends Mock implements api_service.ApiService {}
class MockHttpClient extends Mock implements http.Client {}

void main() {
  group('SearchService', () {
    late SearchService searchService;
    late MockApiService mockApiService;
    late MockHttpClient mockHttpClient;

    setUp(() {
      mockApiService = MockApiService();
      mockHttpClient = MockHttpClient();
      searchService = SearchService(
        apiService: mockApiService,
        client: mockHttpClient,
      );
    });

    tearDown(() {
      searchService.dispose();
    });

    group('search', () {
      test('should return search results when API call succeeds', () async {
        // Arrange
        final mockResponse = api_service.api_service.ApiResponse(
          data: {
            'query': 'flutter',
            'items': [
              {
                'id': '1',
                'category': 'quests',
                'title': 'Learn Flutter',
                'description': 'A quest to learn Flutter development',
                'relevance_score': 0.95,
                'metadata': {},
              }
            ],
            'total_count': 1,
            'page': 1,
            'limit': 20,
            'has_more': false,
            'category_counts': {'quests': 1},
          },
          statusCode: 200,
          success: true,
        );

        when(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await searchService.search(query: 'flutter');

        // Assert
        expect(result.query, equals('flutter'));
        expect(result.items.length, equals(1));
        expect(result.items.first.title, equals('Learn Flutter'));
        expect(result.totalCount, equals(1));
        expect(result.hasMore, isFalse);
      });

      test('should return empty result when API call fails', () async {
        // Arrange
        when(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .thenAnswer((_) async => api_service.api_service.ApiResponse(
              data: null,
              statusCode: 500,
              success: false,
              error: 'Server error',
            ));

        // Act
        final result = await searchService.search(query: 'flutter');

        // Assert
        expect(result.query, equals('flutter'));
        expect(result.items.isEmpty, isTrue);
        expect(result.totalCount, equals(0));
      });

      test('should use cache when available and valid', () async {
        // Arrange
        final mockResponse = api_service.ApiResponse(
          data: {
            'query': 'flutter',
            'items': [],
            'total_count': 0,
            'page': 1,
            'limit': 20,
            'has_more': false,
            'category_counts': {},
          },
          statusCode: 200,
          success: true,
        );

        when(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .thenAnswer((_) async => mockResponse);

        // Act - First call should hit API
        await searchService.search(query: 'flutter');
        
        // Act - Second call should use cache
        await searchService.search(query: 'flutter');

        // Assert - API should only be called once
        verify(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .called(1);
      });

      test('should not use cache when useCache is false', () async {
        // Arrange
        final mockResponse = api_service.ApiResponse(
          data: {
            'query': 'flutter',
            'items': [],
            'total_count': 0,
            'page': 1,
            'limit': 20,
            'has_more': false,
            'category_counts': {},
          },
          statusCode: 200,
          success: true,
        );

        when(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .thenAnswer((_) async => mockResponse);

        // Act - Both calls should hit API
        await searchService.search(query: 'flutter', useCache: false);
        await searchService.search(query: 'flutter', useCache: false);

        // Assert - API should be called twice
        verify(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .called(2);
      });
    });

    group('searchQuests', () {
      test('should return quest list when API call succeeds', () async {
        // Arrange
        final mockResponse = api_service.ApiResponse(
          data: {
            'quests': [
              {
                'id': '1',
                'title': 'Learn Flutter',
                'description': 'A quest to learn Flutter',
                'status': 'active',
                'priority': 'high',
                'tags': ['flutter', 'mobile'],
                'createdAt': '2023-01-01T00:00:00Z',
                'updatedAt': '2023-01-01T00:00:00Z',
              }
            ]
          },
          statusCode: 200,
          success: true,
        );

        when(() => mockApiService.get('/api/v1/search/quests', queryParameters: any(named: 'queryParameters')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await searchService.searchQuests(
          query: 'flutter',
          status: QuestStatus.active,
          priority: QuestPriority.high,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result.first.title, equals('Learn Flutter'));
        expect(result.first.status, equals(QuestStatus.active));
      });

      test('should return empty list when API call fails', () async {
        // Arrange
        when(() => mockApiService.get('/api/v1/search/quests', queryParameters: any(named: 'queryParameters')))
            .thenAnswer((_) async => api_service.ApiResponse(
              data: null,
              statusCode: 500,
              success: false,
              error: 'Server error',
            ));

        // Act
        final result = await searchService.searchQuests(query: 'flutter');

        // Assert
        expect(result.isEmpty, isTrue);
      });
    });

    group('searchUsers', () {
      test('should return user list when API call succeeds', () async {
        // Arrange
        final mockResponse = api_service.ApiResponse(
          data: {
            'users': [
              {
                'id': '1',
                'email': '<EMAIL>',
                'displayName': 'John Doe',
                'firstName': 'John',
                'lastName': 'Doe',
                'role': 'journeyman',
                'status': 'active',
                'totalPoints': 1000,
                'level': 5,
                'createdAt': '2023-01-01T00:00:00Z',
                'updatedAt': '2023-01-01T00:00:00Z',
              }
            ]
          },
          statusCode: 200,
          success: true,
        );

        when(() => mockApiService.get('/api/v1/search/users', queryParameters: any(named: 'queryParameters')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await searchService.searchUsers(
          query: 'john',
          role: UserRole.journeyman,
          status: UserStatus.active,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result.first.displayName, equals('John Doe'));
        expect(result.first.role, equals(UserRole.journeyman));
      });
    });

    group('getSearchSuggestions', () {
      test('should return suggestions when API call succeeds', () async {
        // Arrange
        final mockResponse = api_service.ApiResponse(
          data: {
            'suggestions': ['flutter', 'flutter development', 'flutter tutorial']
          },
          statusCode: 200,
          success: true,
        );

        when(() => mockApiService.get('/api/v1/search/suggestions', queryParameters: any(named: 'queryParameters')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await searchService.getSearchSuggestions('flu');

        // Assert
        expect(result.length, equals(3));
        expect(result, contains('flutter'));
        expect(result, contains('flutter development'));
      });

      test('should return empty list when API call fails', () async {
        // Arrange
        when(() => mockApiService.get('/api/v1/search/suggestions', queryParameters: any(named: 'queryParameters')))
            .thenAnswer((_) async => api_service.ApiResponse(
              data: null,
              statusCode: 500,
              success: false,
            ));

        // Act
        final result = await searchService.getSearchSuggestions('flu');

        // Assert
        expect(result.isEmpty, isTrue);
      });
    });

    group('clearCache', () {
      test('should clear all cached search results', () async {
        // Arrange
        final mockResponse = api_service.ApiResponse(
          data: {
            'query': 'flutter',
            'items': [],
            'total_count': 0,
            'page': 1,
            'limit': 20,
            'has_more': false,
            'category_counts': {},
          },
          statusCode: 200,
          success: true,
        );

        when(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .thenAnswer((_) async => mockResponse);

        // Act - Cache a search result
        await searchService.search(query: 'flutter');
        
        // Clear cache
        searchService.clearCache();
        
        // Search again - should hit API again
        await searchService.search(query: 'flutter');

        // Assert - API should be called twice (cache was cleared)
        verify(() => mockApiService.post('/api/v1/search', body: any(named: 'body')))
            .called(2);
      });
    });
  });

  group('SearchResult', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'query': 'flutter',
        'items': [
          {
            'id': '1',
            'category': 'quests',
            'title': 'Learn Flutter',
            'description': 'A quest to learn Flutter',
            'relevance_score': 0.95,
            'metadata': {'difficulty': 'beginner'},
          }
        ],
        'total_count': 1,
        'page': 1,
        'limit': 20,
        'has_more': false,
        'category_counts': {'quests': 1},
      };

      // Act
      final result = SearchResult.fromJson(json);

      // Assert
      expect(result.query, equals('flutter'));
      expect(result.items.length, equals(1));
      expect(result.items.first.title, equals('Learn Flutter'));
      expect(result.items.first.category, equals(SearchCategory.quests));
      expect(result.totalCount, equals(1));
      expect(result.hasMore, isFalse);
      expect(result.categoryCounts[SearchCategory.quests], equals(1));
    });

    test('should create empty result correctly', () {
      // Act
      final result = SearchResult.empty('test query');

      // Assert
      expect(result.query, equals('test query'));
      expect(result.items.isEmpty, isTrue);
      expect(result.totalCount, equals(0));
      expect(result.hasMore, isFalse);
      expect(result.categoryCounts.isEmpty, isTrue);
    });
  });

  group('SearchResultItem', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'id': '1',
        'category': 'quests',
        'title': 'Learn Flutter',
        'description': 'A quest to learn Flutter',
        'image_url': 'https://example.com/image.jpg',
        'relevance_score': 0.95,
        'metadata': {'difficulty': 'beginner'},
      };

      // Act
      final item = SearchResultItem.fromJson(json);

      // Assert
      expect(item.id, equals('1'));
      expect(item.category, equals(SearchCategory.quests));
      expect(item.title, equals('Learn Flutter'));
      expect(item.description, equals('A quest to learn Flutter'));
      expect(item.imageUrl, equals('https://example.com/image.jpg'));
      expect(item.relevanceScore, equals(0.95));
      expect(item.metadata['difficulty'], equals('beginner'));
    });
  });
}
