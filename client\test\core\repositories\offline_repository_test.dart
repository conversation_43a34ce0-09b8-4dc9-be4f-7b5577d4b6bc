import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:quester_client/core/repositories/offline_repository.dart';
import 'package:quester_client/core/services/api_service.dart';
import 'package:quester_client/core/services/cache_service.dart';
import 'package:quester_client/core/services/sync_service.dart';

// Fake classes for mocktail
class FakePendingOperation extends Fake implements PendingOperation {}

// Mock classes
class MockApiService extends Mock implements ApiService {}
class MockCacheService extends Mock implements CacheService {}
class MockSyncService extends Mock implements SyncService {}
class MockConnectivity extends Mock implements Connectivity {}

// Test model
class TestItem {
  final String id;
  final String name;
  final String description;

  const TestItem({
    required this.id,
    required this.name,
    required this.description,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
  };

  factory TestItem.fromJson(Map<String, dynamic> json) => TestItem(
    id: json['id'] as String,
    name: json['name'] as String,
    description: json['description'] as String,
  );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TestItem &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          description == other.description;

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ description.hashCode;
}

// Concrete implementation for testing
class TestRepository extends OfflineRepository<TestItem> {
  TestRepository({
    required super.apiService,
    required super.cacheService,
    required super.syncService,
    super.connectivity,
  }) : super(repositoryName: 'test_items');

  @override
  Future<TestItem?> fetchFromApi(String id) async {
    final response = await apiService.get('/api/test/$id');
    if (response.success && response.data != null) {
      return TestItem.fromJson(response.data as Map<String, dynamic>);
    }
    return null;
  }

  @override
  Future<List<TestItem>> fetchListFromApi({
    Map<String, dynamic>? filters,
    int? page,
    int? limit,
  }) async {
    final response = await apiService.get('/api/test');
    if (response.success && response.data != null) {
      final List<dynamic> items = response.data['items'] as List<dynamic>;
      return items.map((json) => TestItem.fromJson(json as Map<String, dynamic>)).toList();
    }
    return [];
  }

  @override
  Future<TestItem?> createViaApi(Map<String, dynamic> data) async {
    final response = await apiService.post('/api/test', body: data);
    if (response.success && response.data != null) {
      return TestItem.fromJson(response.data as Map<String, dynamic>);
    }
    return null;
  }

  @override
  Future<TestItem?> updateViaApi(String id, Map<String, dynamic> data) async {
    final response = await apiService.post('/api/test/$id', body: data);
    if (response.success && response.data != null) {
      return TestItem.fromJson(response.data as Map<String, dynamic>);
    }
    return null;
  }

  @override
  Future<bool> deleteViaApi(String id) async {
    final response = await apiService.post('/api/test/$id/delete');
    return response.success;
  }

  @override
  String getCreateEndpoint() => '/api/test';

  @override
  String getUpdateEndpoint(String id) => '/api/test/$id';

  @override
  String getDeleteEndpoint(String id) => '/api/test/$id';

  String _extractId(TestItem item) => item.id;

  @override
  TestItem? createOptimisticResult(Map<String, dynamic> data) {
    return TestItem(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      name: data['name'] as String,
      description: data['description'] as String,
    );
  }

  @override
  TestItem updateOptimistically(TestItem item, Map<String, dynamic> data) {
    return TestItem(
      id: item.id,
      name: data['name'] as String? ?? item.name,
      description: data['description'] as String? ?? item.description,
    );
  }
}

void main() {
  group('OfflineRepository', () {
    late TestRepository repository;
    late MockApiService mockApiService;
    late MockCacheService mockCacheService;
    late MockSyncService mockSyncService;
    late MockConnectivity mockConnectivity;

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(FakePendingOperation());
    });

    setUp(() {
      mockApiService = MockApiService();
      mockCacheService = MockCacheService();
      mockSyncService = MockSyncService();
      mockConnectivity = MockConnectivity();

      repository = TestRepository(
        apiService: mockApiService,
        cacheService: mockCacheService,
        syncService: mockSyncService,
        connectivity: mockConnectivity,
      );

      // Set up default connectivity
      when(() => mockConnectivity.checkConnectivity())
          .thenAnswer((_) async => [ConnectivityResult.wifi]);
    });

    group('get', () {
      test('should return cached data when available and not force refresh', () async {
        // Arrange
        const testItem = TestItem(id: '1', name: 'Test', description: 'Description');
        when(() => mockCacheService.get<TestItem>(any()))
            .thenAnswer((_) async => testItem);

        // Act
        final result = await repository.get('1');

        // Assert
        expect(result, equals(testItem));
        verify(() => mockCacheService.get<TestItem>('test_items_item_1')).called(1);
        verifyNever(() => mockApiService.get(any()));
      });

      test('should fetch from API when cache miss and online', () async {
        // Arrange
        const testItem = TestItem(id: '1', name: 'Test', description: 'Description');
        
        when(() => mockCacheService.get<TestItem>(any()))
            .thenAnswer((_) async => null);
        when(() => mockApiService.get('/api/test/1'))
            .thenAnswer((_) async => ApiResponse(
              data: testItem.toJson(),
              statusCode: 200,
              success: true,
            ));
        when(() => mockCacheService.set(any(), any(), ttl: any(named: 'ttl')))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.get('1');

        // Assert
        expect(result, equals(testItem));
        verify(() => mockApiService.get('/api/test/1')).called(1);
        verify(() => mockCacheService.set('test_items_item_1', testItem, ttl: any(named: 'ttl'))).called(1);
      });

      test('should return cached data when offline', () async {
        // Arrange
        const testItem = TestItem(id: '1', name: 'Test', description: 'Description');
        
        when(() => mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => [ConnectivityResult.none]);
        when(() => mockCacheService.get<TestItem>(any()))
            .thenAnswer((_) async => testItem);

        // Act
        final result = await repository.get('1');

        // Assert
        expect(result, equals(testItem));
        verify(() => mockCacheService.get<TestItem>('test_items_item_1')).called(1);
        verifyNever(() => mockApiService.get(any()));
      });

      test('should force refresh when forceRefresh is true', () async {
        // Arrange
        const testItem = TestItem(id: '1', name: 'Test', description: 'Description');
        
        when(() => mockApiService.get('/api/test/1'))
            .thenAnswer((_) async => ApiResponse(
              data: testItem.toJson(),
              statusCode: 200,
              success: true,
            ));
        when(() => mockCacheService.set(any(), any(), ttl: any(named: 'ttl')))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.get('1', forceRefresh: true);

        // Assert
        expect(result, equals(testItem));
        verify(() => mockApiService.get('/api/test/1')).called(1);
        verifyNever(() => mockCacheService.get<TestItem>(any()));
      });
    });

    group('getList', () {
      test('should return cached list when available', () async {
        // Arrange
        const testItems = [
          TestItem(id: '1', name: 'Test 1', description: 'Description 1'),
          TestItem(id: '2', name: 'Test 2', description: 'Description 2'),
        ];
        
        when(() => mockCacheService.get<List<TestItem>>(any()))
            .thenAnswer((_) async => testItems);

        // Act
        final result = await repository.getList();

        // Assert
        expect(result, equals(testItems));
        verify(() => mockCacheService.get<List<TestItem>>(any())).called(1);
        verifyNever(() => mockApiService.get(any()));
      });

      test('should fetch from API when cache miss and online', () async {
        // Arrange
        const testItems = [
          TestItem(id: '1', name: 'Test 1', description: 'Description 1'),
          TestItem(id: '2', name: 'Test 2', description: 'Description 2'),
        ];
        
        when(() => mockCacheService.get<List<TestItem>>(any()))
            .thenAnswer((_) async => null);
        when(() => mockApiService.get('/api/test'))
            .thenAnswer((_) async => ApiResponse(
              data: {
                'items': testItems.map((item) => item.toJson()).toList(),
              },
              statusCode: 200,
              success: true,
            ));
        when(() => mockCacheService.set(any(), any(), ttl: any(named: 'ttl')))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.getList();

        // Assert
        expect(result, equals(testItems));
        verify(() => mockApiService.get('/api/test')).called(1);
        verify(() => mockCacheService.set(any(), testItems, ttl: any(named: 'ttl'))).called(1);
      });
    });

    group('create', () {
      test('should create via API when online', () async {
        // Arrange
        final data = {'name': 'New Item', 'description': 'New Description'};
        const createdItem = TestItem(id: '1', name: 'New Item', description: 'New Description');
        
        when(() => mockApiService.post('/api/test', body: data))
            .thenAnswer((_) async => ApiResponse(
              data: createdItem.toJson(),
              statusCode: 201,
              success: true,
            ));
        when(() => mockCacheService.set(any(), any(), ttl: any(named: 'ttl')))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.create(data);

        // Assert
        expect(result, equals(createdItem));
        verify(() => mockApiService.post('/api/test', body: data)).called(1);
        verify(() => mockCacheService.set('test_items_item_1', createdItem, ttl: any(named: 'ttl'))).called(1);
      });

      test('should queue for sync when offline', () async {
        // Arrange
        final data = {'name': 'New Item', 'description': 'New Description'};
        
        when(() => mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => [ConnectivityResult.none]);
        when(() => mockSyncService.addPendingOperation(any()))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.create(data);

        // Assert
        expect(result, isNotNull);
        expect(result!.name, equals('New Item'));
        expect(result.description, equals('New Description'));
        verify(() => mockSyncService.addPendingOperation(any())).called(1);
      });
    });

    group('update', () {
      test('should update via API when online', () async {
        // Arrange
        const id = '1';
        final data = {'name': 'Updated Item', 'description': 'Updated Description'};
        const updatedItem = TestItem(id: '1', name: 'Updated Item', description: 'Updated Description');
        
        when(() => mockApiService.post('/api/test/1', body: data))
            .thenAnswer((_) async => ApiResponse(
              data: updatedItem.toJson(),
              statusCode: 200,
              success: true,
            ));
        when(() => mockCacheService.set(any(), any(), ttl: any(named: 'ttl')))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.update(id, data);

        // Assert
        expect(result, equals(updatedItem));
        verify(() => mockApiService.post('/api/test/1', body: data)).called(1);
        verify(() => mockCacheService.set('test_items_item_1', updatedItem, ttl: any(named: 'ttl'))).called(1);
      });

      test('should update optimistically when offline', () async {
        // Arrange
        const id = '1';
        final data = {'name': 'Updated Item', 'description': 'Updated Description'};
        const originalItem = TestItem(id: '1', name: 'Original Item', description: 'Original Description');
        
        when(() => mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => [ConnectivityResult.none]);
        when(() => mockSyncService.addPendingOperation(any()))
            .thenAnswer((_) async {});
        when(() => mockCacheService.get<TestItem>('test_items_item_1'))
            .thenAnswer((_) async => originalItem);
        when(() => mockCacheService.set(any(), any(), ttl: any(named: 'ttl')))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.update(id, data);

        // Assert
        expect(result, isNotNull);
        expect(result!.name, equals('Updated Item'));
        expect(result.description, equals('Updated Description'));
        verify(() => mockSyncService.addPendingOperation(any())).called(1);
        verify(() => mockCacheService.set('test_items_item_1', any(), ttl: any(named: 'ttl'))).called(1);
      });
    });

    group('delete', () {
      test('should delete via API when online', () async {
        // Arrange
        const id = '1';
        
        when(() => mockApiService.post('/api/test/1/delete'))
            .thenAnswer((_) async => ApiResponse(
              data: null,
              statusCode: 200,
              success: true,
            ));
        when(() => mockCacheService.remove(any()))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.delete(id);

        // Assert
        expect(result, isTrue);
        verify(() => mockApiService.post('/api/test/1/delete')).called(1);
        verify(() => mockCacheService.remove('test_items_item_1')).called(1);
      });

      test('should queue for sync when offline', () async {
        // Arrange
        const id = '1';
        
        when(() => mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => [ConnectivityResult.none]);
        when(() => mockSyncService.addPendingOperation(any()))
            .thenAnswer((_) async {});
        when(() => mockCacheService.remove(any()))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.delete(id);

        // Assert
        expect(result, isTrue);
        verify(() => mockSyncService.addPendingOperation(any())).called(1);
        verify(() => mockCacheService.remove('test_items_item_1')).called(1);
      });
    });
  });
}
