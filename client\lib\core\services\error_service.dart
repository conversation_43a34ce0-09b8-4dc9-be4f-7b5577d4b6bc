import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Comprehensive error handling service for the Quester application
/// Provides centralized error handling, user feedback, and error reporting
class ErrorService {
  static final ErrorService _instance = ErrorService._internal();
  factory ErrorService() => _instance;
  ErrorService._internal();

  // Error tracking
  final List<AppError> _errorHistory = [];
  final StreamController<AppError> _errorController = StreamController<AppError>.broadcast();
  
  // Configuration
  bool _showErrorSnackbars = true;
  bool _logErrors = true;
  bool _reportErrors = true;
  
  /// Stream of errors for listening
  Stream<AppError> get errorStream => _errorController.stream;
  
  /// Error history
  List<AppError> get errorHistory => List.unmodifiable(_errorHistory);
  
  /// Configure error service behavior
  void configure({
    bool? showErrorSnackbars,
    bool? logErrors,
    bool? reportErrors,
  }) {
    _showErrorSnackbars = showErrorSnackbars ?? _showErrorSnackbars;
    _logErrors = logErrors ?? _logErrors;
    _reportErrors = reportErrors ?? _reportErrors;
  }

  /// Handle an error with appropriate user feedback
  void handleError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    ErrorSeverity severity = ErrorSeverity.medium,
    String? userMessage,
    Map<String, dynamic>? metadata,
    bool showToUser = true,
  }) {
    final appError = AppError(
      error: error,
      stackTrace: stackTrace,
      context: context,
      severity: severity,
      userMessage: userMessage,
      metadata: metadata,
      timestamp: DateTime.now(),
    );

    _processError(appError, showToUser: showToUser);
  }

  /// Handle API errors specifically
  void handleApiError(
    dynamic error, {
    int? statusCode,
    String? endpoint,
    String? method,
    Map<String, dynamic>? requestData,
    bool showToUser = true,
  }) {
    final userMessage = _getApiErrorMessage(statusCode, error);
    
    handleError(
      error,
      context: 'API Error',
      severity: _getApiErrorSeverity(statusCode),
      userMessage: userMessage,
      metadata: {
        'status_code': statusCode,
        'endpoint': endpoint,
        'method': method,
        'request_data': requestData,
      },
      showToUser: showToUser,
    );
  }

  /// Handle network errors
  void handleNetworkError(
    dynamic error, {
    String? context,
    bool showToUser = true,
  }) {
    handleError(
      error,
      context: context ?? 'Network Error',
      severity: ErrorSeverity.high,
      userMessage: 'Network connection error. Please check your internet connection and try again.',
      showToUser: showToUser,
    );
  }

  /// Handle validation errors
  void handleValidationError(
    String message, {
    Map<String, List<String>>? fieldErrors,
    bool showToUser = true,
  }) {
    handleError(
      message,
      context: 'Validation Error',
      severity: ErrorSeverity.low,
      userMessage: message,
      metadata: {'field_errors': fieldErrors},
      showToUser: showToUser,
    );
  }

  /// Show error dialog
  void showErrorDialog(
    BuildContext context,
    AppError error, {
    String? title,
    List<Widget>? actions,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getErrorIcon(error.severity),
              color: _getErrorColor(error.severity),
            ),
            const SizedBox(width: 8),
            Text(title ?? 'Error'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(error.userMessage ?? error.error.toString()),
            if (error.context != null) ...[
              const SizedBox(height: 8),
              Text(
                'Context: ${error.context}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ],
        ),
        actions: actions ?? [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          if (error.severity == ErrorSeverity.high) ...[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _reportError(error);
              },
              child: const Text('Report'),
            ),
          ],
        ],
      ),
    );
  }

  /// Show error snackbar
  void showErrorSnackbar(
    BuildContext context,
    AppError error, {
    Duration duration = const Duration(seconds: 4),
  }) {
    if (!_showErrorSnackbars) return;

    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(
            _getErrorIcon(error.severity),
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(error.userMessage ?? error.error.toString()),
          ),
        ],
      ),
      backgroundColor: _getErrorColor(error.severity),
      duration: duration,
      action: error.severity == ErrorSeverity.high
          ? SnackBarAction(
              label: 'Report',
              textColor: Colors.white,
              onPressed: () => _reportError(error),
            )
          : null,
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// Process error internally
  void _processError(AppError error, {bool showToUser = true}) {
    // Add to history
    _errorHistory.add(error);
    if (_errorHistory.length > 100) {
      _errorHistory.removeAt(0); // Keep only last 100 errors
    }

    // Emit error event
    _errorController.add(error);

    // Log error
    if (_logErrors) {
      _logError(error);
    }

    // Report error if critical
    if (_reportErrors && error.severity == ErrorSeverity.critical) {
      _reportError(error);
    }
  }

  /// Log error to console/logging service
  void _logError(AppError error) {
    final message = 'Error: ${error.error}';
    final details = [
      if (error.context != null) 'Context: ${error.context}',
      'Severity: ${error.severity.name}',
      'Timestamp: ${error.timestamp}',
      if (error.metadata != null) 'Metadata: ${error.metadata}',
    ].join('\n');

    developer.log(
      '$message\n$details',
      name: 'ErrorService',
      error: error.error,
      stackTrace: error.stackTrace,
    );
  }

  /// Report error to external service
  void _reportError(AppError error) {
    // In a real app, this would send to crash reporting service
    // like Crashlytics, Sentry, or Bugsnag
    developer.log('Reporting error: ${error.error}', name: 'ErrorService');
  }

  /// Get user-friendly API error message
  String _getApiErrorMessage(int? statusCode, dynamic error) {
    switch (statusCode) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in and try again.';
      case 403:
        return 'Access denied. You don\'t have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 503:
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return error?.toString() ?? 'An unexpected error occurred.';
    }
  }

  /// Get error severity based on status code
  ErrorSeverity _getApiErrorSeverity(int? statusCode) {
    switch (statusCode) {
      case 400:
      case 422:
        return ErrorSeverity.low;
      case 401:
      case 403:
      case 404:
        return ErrorSeverity.medium;
      case 500:
      case 502:
      case 503:
        return ErrorSeverity.high;
      default:
        return ErrorSeverity.medium;
    }
  }

  /// Get error icon based on severity
  IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Icons.info_outline;
      case ErrorSeverity.medium:
        return Icons.warning_amber_outlined;
      case ErrorSeverity.high:
        return Icons.error_outline;
      case ErrorSeverity.critical:
        return Icons.dangerous_outlined;
    }
  }

  /// Get error color based on severity
  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Colors.blue;
      case ErrorSeverity.medium:
        return Colors.orange;
      case ErrorSeverity.high:
        return Colors.red;
      case ErrorSeverity.critical:
        return Colors.red.shade900;
    }
  }

  /// Clear error history
  void clearHistory() {
    _errorHistory.clear();
  }

  /// Dispose resources
  void dispose() {
    _errorController.close();
  }
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Application error model
class AppError {
  final dynamic error;
  final StackTrace? stackTrace;
  final String? context;
  final ErrorSeverity severity;
  final String? userMessage;
  final Map<String, dynamic>? metadata;
  final DateTime timestamp;

  const AppError({
    required this.error,
    this.stackTrace,
    this.context,
    required this.severity,
    this.userMessage,
    this.metadata,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'AppError(error: $error, context: $context, severity: $severity, timestamp: $timestamp)';
  }
}

/// Error handling mixin for widgets
mixin ErrorHandlingMixin<T extends StatefulWidget> on State<T> {
  final ErrorService _errorService = ErrorService();

  /// Handle error with automatic UI feedback
  void handleError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    ErrorSeverity severity = ErrorSeverity.medium,
    String? userMessage,
    bool showSnackbar = true,
    bool showDialog = false,
  }) {
    _errorService.handleError(
      error,
      stackTrace: stackTrace,
      context: context,
      severity: severity,
      userMessage: userMessage,
      showToUser: false, // We'll handle UI feedback manually
    );

    if (showSnackbar && mounted) {
      _errorService.showErrorSnackbar(
        context,
        AppError(
          error: error,
          stackTrace: stackTrace,
          context: context,
          severity: severity,
          userMessage: userMessage,
          timestamp: DateTime.now(),
        ),
      );
    }

    if (showDialog && mounted) {
      _errorService.showErrorDialog(
        context,
        AppError(
          error: error,
          stackTrace: stackTrace,
          context: context,
          severity: severity,
          userMessage: userMessage,
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// Handle API error with automatic UI feedback
  void handleApiError(
    dynamic error, {
    int? statusCode,
    String? endpoint,
    bool showSnackbar = true,
  }) {
    _errorService.handleApiError(
      error,
      statusCode: statusCode,
      endpoint: endpoint,
      showToUser: false,
    );

    if (showSnackbar && mounted) {
      final userMessage = _errorService._getApiErrorMessage(statusCode, error);
      _errorService.showErrorSnackbar(
        context,
        AppError(
          error: error,
          context: 'API Error',
          severity: _errorService._getApiErrorSeverity(statusCode),
          userMessage: userMessage,
          timestamp: DateTime.now(),
        ),
      );
    }
  }
}
