import 'package:test/test.dart';
import '../../lib/services/email_service.dart';

void main() {
  group('EmailService', () {
    late EmailService emailService;

    setUp(() {
      emailService = EmailService(
        provider: 'mock',
        config: {
          'baseUrl': 'http://localhost:8080',
          'fromEmail': '<EMAIL>',
          'fromName': 'Quester Test',
        },
      );
    });

    group('sendVerificationEmail', () {
      test('should send verification email successfully', () async {
        // Act
        final result = await emailService.sendVerificationEmail(
          '<EMAIL>',
          'test-token-123',
        );

        // Assert
        expect(result, isTrue);
      });

      test('should handle email sending failure gracefully', () async {
        // Arrange
        final failingEmailService = EmailService(
          provider: 'invalid',
          config: {},
        );

        // Act
        final result = await failingEmailService.sendVerificationEmail(
          '<EMAIL>',
          'test-token-123',
        );

        // Assert
        expect(result, isFalse);
      });
    });

    group('sendPasswordResetEmail', () {
      test('should send password reset email successfully', () async {
        // Act
        final result = await emailService.sendPasswordResetEmail(
          '<EMAIL>',
          'reset-token-123',
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('sendMFACode', () {
      test('should send MFA code email successfully', () async {
        // Act
        final result = await emailService.sendMFACode(
          '<EMAIL>',
          '123456',
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('sendWelcomeEmail', () {
      test('should send welcome email successfully', () async {
        // Act
        final result = await emailService.sendWelcomeEmail(
          '<EMAIL>',
          'John Doe',
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('different providers', () {
      test('should handle SendGrid provider', () async {
        // Arrange
        final sendGridService = EmailService(
          provider: 'sendgrid',
          config: {
            'sendgridApiKey': 'test-key',
            'fromEmail': '<EMAIL>',
          },
        );

        // Act
        final result = await sendGridService.sendVerificationEmail(
          '<EMAIL>',
          'token-123',
        );

        // Assert - Should fail due to invalid API key but not crash
        expect(result, isFalse);
      });

      test('should handle AWS SES provider', () async {
        // Arrange
        final sesService = EmailService(
          provider: 'ses',
          config: {},
        );

        // Act
        final result = await sesService.sendVerificationEmail(
          '<EMAIL>',
          'token-123',
        );

        // Assert - Should use mock implementation
        expect(result, isTrue);
      });

      test('should handle SMTP provider', () async {
        // Arrange
        final smtpService = EmailService(
          provider: 'smtp',
          config: {},
        );

        // Act
        final result = await smtpService.sendVerificationEmail(
          '<EMAIL>',
          'token-123',
        );

        // Assert - Should use mock implementation
        expect(result, isTrue);
      });
    });
  });
}
