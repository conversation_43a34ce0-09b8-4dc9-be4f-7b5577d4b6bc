import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../lib/core/services/cache_service.dart';

void main() {
  group('CacheService', () {
    late CacheService cacheService;

    setUp(() async {
      // Initialize SharedPreferences with empty data
      SharedPreferences.setMockInitialValues({});
      
      cacheService = CacheService();
      await cacheService.initialize();
    });

    tearDown(() async {
      await cacheService.clear();
    });

    group('set and get', () {
      test('should store and retrieve data correctly', () async {
        // Arrange
        const key = 'test_key';
        final data = {'name': 'John', 'age': 30};

        // Act
        await cacheService.set(key, data);
        final result = await cacheService.get<Map<String, dynamic>>(key);

        // Assert
        expect(result, isNotNull);
        expect(result!['name'], equals('John'));
        expect(result['age'], equals(30));
      });

      test('should return null for non-existent key', () async {
        // Act
        final result = await cacheService.get('non_existent_key');

        // Assert
        expect(result, isNull);
      });

      test('should respect TTL and return null for expired data', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';
        const ttl = Duration(milliseconds: 100);

        // Act
        await cacheService.set(key, data, ttl: ttl);
        
        // Wait for expiration
        await Future.delayed(const Duration(milliseconds: 150));
        
        final result = await cacheService.get(key);

        // Assert
        expect(result, isNull);
      });

      test('should store data in memory cache by default', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';

        // Act
        await cacheService.set(key, data, level: CacheLevel.memory);
        final result = await cacheService.get(key);

        // Assert
        expect(result, equals(data));
      });

      test('should store data in disk cache', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';

        // Act
        await cacheService.set(key, data, level: CacheLevel.disk);
        final result = await cacheService.get(key);

        // Assert
        expect(result, equals(data));
      });

      test('should store data in both memory and disk cache', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';

        // Act
        await cacheService.set(key, data, level: CacheLevel.both);
        final result = await cacheService.get(key);

        // Assert
        expect(result, equals(data));
      });
    });

    group('has', () {
      test('should return true for existing key', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';

        await cacheService.set(key, data);

        // Act
        final exists = await cacheService.has(key);

        // Assert
        expect(exists, isTrue);
      });

      test('should return false for non-existent key', () async {
        // Act
        final exists = await cacheService.has('non_existent_key');

        // Assert
        expect(exists, isFalse);
      });

      test('should return false for expired key', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';
        const ttl = Duration(milliseconds: 100);

        await cacheService.set(key, data, ttl: ttl);
        
        // Wait for expiration
        await Future.delayed(const Duration(milliseconds: 150));

        // Act
        final exists = await cacheService.has(key);

        // Assert
        expect(exists, isFalse);
      });
    });

    group('remove', () {
      test('should remove data from cache', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';

        await cacheService.set(key, data);
        expect(await cacheService.has(key), isTrue);

        // Act
        await cacheService.remove(key);

        // Assert
        expect(await cacheService.has(key), isFalse);
        expect(await cacheService.get(key), isNull);
      });
    });

    group('clear', () {
      test('should remove all data from cache', () async {
        // Arrange
        await cacheService.set('key1', 'data1');
        await cacheService.set('key2', 'data2');
        await cacheService.set('key3', 'data3');

        expect(await cacheService.has('key1'), isTrue);
        expect(await cacheService.has('key2'), isTrue);
        expect(await cacheService.has('key3'), isTrue);

        // Act
        await cacheService.clear();

        // Assert
        expect(await cacheService.has('key1'), isFalse);
        expect(await cacheService.has('key2'), isFalse);
        expect(await cacheService.has('key3'), isFalse);
      });
    });

    group('getStats', () {
      test('should return cache statistics', () async {
        // Arrange
        await cacheService.set('key1', 'data1', level: CacheLevel.memory);
        await cacheService.set('key2', 'data2', level: CacheLevel.disk);
        await cacheService.set('key3', 'data3', level: CacheLevel.both);

        // Act
        final stats = await cacheService.getStats();

        // Assert
        expect(stats.memoryEntries, greaterThan(0));
        expect(stats.diskEntries, greaterThan(0));
        expect(stats.memorySize, greaterThan(0));
      });
    });

    group('cleanup', () {
      test('should remove expired entries', () async {
        // Arrange
        const shortTTL = Duration(milliseconds: 100);
        const longTTL = Duration(seconds: 10);

        await cacheService.set('expired_key', 'data1', ttl: shortTTL);
        await cacheService.set('valid_key', 'data2', ttl: longTTL);

        // Wait for first item to expire
        await Future.delayed(const Duration(milliseconds: 150));

        // Act
        await cacheService.cleanup();

        // Assert
        expect(await cacheService.has('expired_key'), isFalse);
        expect(await cacheService.has('valid_key'), isTrue);
      });
    });

    group('access tracking', () {
      test('should update access count when retrieving data', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';

        await cacheService.set(key, data);

        // Act - Access the data multiple times
        await cacheService.get(key);
        await cacheService.get(key);
        await cacheService.get(key);

        // The access count is internal, so we can't directly test it
        // But we can verify the data is still accessible
        final result = await cacheService.get(key);
        expect(result, equals(data));
      });

      test('should not update access time when updateAccessTime is false', () async {
        // Arrange
        const key = 'test_key';
        const data = 'test_data';

        await cacheService.set(key, data);

        // Act
        final result = await cacheService.get(key, updateAccessTime: false);

        // Assert
        expect(result, equals(data));
        // Access time update is internal, so we can't directly test it
        // But we can verify the operation completed successfully
      });
    });

    group('data types', () {
      test('should handle string data', () async {
        const key = 'string_key';
        const data = 'Hello, World!';

        await cacheService.set(key, data);
        final result = await cacheService.get<String>(key);

        expect(result, equals(data));
      });

      test('should handle integer data', () async {
        const key = 'int_key';
        const data = 42;

        await cacheService.set(key, data);
        final result = await cacheService.get<int>(key);

        expect(result, equals(data));
      });

      test('should handle list data', () async {
        const key = 'list_key';
        final data = [1, 2, 3, 'four', 5.0];

        await cacheService.set(key, data);
        final result = await cacheService.get<List>(key);

        expect(result, equals(data));
      });

      test('should handle map data', () async {
        const key = 'map_key';
        final data = {
          'string': 'value',
          'number': 123,
          'boolean': true,
          'list': [1, 2, 3],
          'nested': {'key': 'value'}
        };

        await cacheService.set(key, data);
        final result = await cacheService.get<Map<String, dynamic>>(key);

        expect(result, equals(data));
      });
    });
  });

  group('CacheEntry', () {
    test('should create correctly with all properties', () {
      // Arrange
      final now = DateTime.now();
      final expiresAt = now.add(const Duration(hours: 1));

      // Act
      final entry = CacheEntry(
        key: 'test_key',
        data: 'test_data',
        createdAt: now,
        expiresAt: expiresAt,
        accessCount: 5,
        lastAccessed: now,
        compressed: false,
        size: 100,
      );

      // Assert
      expect(entry.key, equals('test_key'));
      expect(entry.data, equals('test_data'));
      expect(entry.createdAt, equals(now));
      expect(entry.expiresAt, equals(expiresAt));
      expect(entry.accessCount, equals(5));
      expect(entry.lastAccessed, equals(now));
      expect(entry.compressed, isFalse);
      expect(entry.size, equals(100));
    });

    test('should detect expired entries correctly', () {
      // Arrange
      final pastTime = DateTime.now().subtract(const Duration(hours: 1));
      final futureTime = DateTime.now().add(const Duration(hours: 1));

      final expiredEntry = CacheEntry(
        key: 'expired',
        data: 'data',
        createdAt: pastTime,
        expiresAt: pastTime,
        accessCount: 0,
        lastAccessed: pastTime,
        compressed: false,
        size: 0,
      );

      final validEntry = CacheEntry(
        key: 'valid',
        data: 'data',
        createdAt: DateTime.now(),
        expiresAt: futureTime,
        accessCount: 0,
        lastAccessed: DateTime.now(),
        compressed: false,
        size: 0,
      );

      // Assert
      expect(expiredEntry.isExpired, isTrue);
      expect(validEntry.isExpired, isFalse);
    });

    test('should copy with updated access correctly', () {
      // Arrange
      final now = DateTime.now();
      final entry = CacheEntry(
        key: 'test_key',
        data: 'test_data',
        createdAt: now,
        expiresAt: now.add(const Duration(hours: 1)),
        accessCount: 5,
        lastAccessed: now,
        compressed: false,
        size: 100,
      );

      // Act
      final updatedEntry = entry.copyWithAccess();

      // Assert
      expect(updatedEntry.key, equals(entry.key));
      expect(updatedEntry.data, equals(entry.data));
      expect(updatedEntry.accessCount, equals(6)); // Incremented
      expect(updatedEntry.lastAccessed.isAfter(entry.lastAccessed), isTrue);
    });

    test('should serialize to and from JSON correctly', () {
      // Arrange
      final now = DateTime.now();
      final entry = CacheEntry(
        key: 'test_key',
        data: {'nested': 'data'},
        createdAt: now,
        expiresAt: now.add(const Duration(hours: 1)),
        accessCount: 5,
        lastAccessed: now,
        compressed: false,
        size: 100,
      );

      // Act
      final json = entry.toJson();
      final reconstructed = CacheEntry.fromJson(json);

      // Assert
      expect(reconstructed.key, equals(entry.key));
      expect(reconstructed.data, equals(entry.data));
      expect(reconstructed.createdAt, equals(entry.createdAt));
      expect(reconstructed.expiresAt, equals(entry.expiresAt));
      expect(reconstructed.accessCount, equals(entry.accessCount));
      expect(reconstructed.lastAccessed, equals(entry.lastAccessed));
      expect(reconstructed.compressed, equals(entry.compressed));
      expect(reconstructed.size, equals(entry.size));
    });
  });

  group('CacheStats', () {
    test('should calculate hit rate correctly', () {
      // Test with hits
      final stats1 = CacheStats(
        memoryEntries: 10,
        diskEntries: 5,
        memorySize: 1000,
        totalHits: 30,
      );
      expect(stats1.hitRate, equals(2.0)); // 30 hits / 15 entries

      // Test with no hits
      final stats2 = CacheStats(
        memoryEntries: 10,
        diskEntries: 5,
        memorySize: 1000,
        totalHits: 0,
      );
      expect(stats2.hitRate, equals(0.0));
    });

    test('should have meaningful toString', () {
      final stats = CacheStats(
        memoryEntries: 10,
        diskEntries: 5,
        memorySize: 1000,
        totalHits: 30,
      );

      final string = stats.toString();
      expect(string, contains('memory: 10'));
      expect(string, contains('disk: 5'));
      expect(string, contains('size: 1000B'));
      expect(string, contains('hits: 30'));
      expect(string, contains('hit rate: 200.0%'));
    });
  });
}
