import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';
import 'package:shared/shared.dart';
import 'database_service.dart';
import 'mfa_service.dart';
import 'audit_log_service.dart';
import 'logging_service.dart';

/// Comprehensive authentication service with enterprise features
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseService _dbService = DatabaseService();
  final MFAService _mfaService = MFAService();
  final AuditLogService _auditService = AuditLogService();
  final Random _random = Random.secure();
  final Uuid _uuid = const Uuid();
  final Map<String, AuthSession> _activeSessions = <String, AuthSession>{};

  // JWT configuration - Use configuration constants for development
  static const String _jwtSecret = 'quester-dev-secret-key-for-development-only-change-in-production';
  static const int _accessTokenExpiryHours = 2;
  // ignore: unused_field
  static const int _refreshTokenExpiryDays = 30;
  // ignore: unused_field
  static const int _twoFactorTimeoutMinutes = 10;

  /// Initialize authentication service
  Future<void> initialize() async {
    await _dbService.initialize();
    print('🔐 AuthService initialized');
  }

  /// Authenticate user with email and password
  Future<Map<String, dynamic>> authenticate({
    required String email,
    required String password,
    String? twoFactorCode,
    String? organizationId,
    Map<String, String>? deviceInfo,
    bool rememberMe = false,
  }) async {
    try {
      // Find user by email
      final user = await _findUserByEmail(email);
      if (user == null) {
        return _errorResponse('Invalid credentials', 'AUTH_INVALID_CREDENTIALS');
      }

      // Check if account is active or pending verification
      if (user['status'] != 'active' && user['status'] != 'pending_verification') {
        return _errorResponse('Account is not active', 'AUTH_ACCOUNT_INACTIVE');
      }

      // Verify password
      final passwordHash = user['password_hash'] as String;
      if (!_verifyPassword(password, passwordHash)) {
        await _logAuthEvent(
          userId: user['id'],
          action: 'login_failed',
          success: false,
          metadata: {'reason': 'invalid_password'},
          ipAddress: deviceInfo?['ip'] ?? 'unknown',
          userAgent: deviceInfo?['userAgent'] ?? 'unknown',
        );
        return _errorResponse('Invalid credentials', 'AUTH_INVALID_CREDENTIALS');
      }

      // Check if 2FA is required
      final requires2FA = user['two_factor_enabled'] as bool? ?? false;
      if (requires2FA && twoFactorCode == null) {
        final sessionToken = await _createTwoFactorSession(user['id']);
        return {
          'success': false,
          'requiresTwoFactor': true,
          'sessionToken': sessionToken,
          'availableMethods': await _getTwoFactorMethods(user['id']),
        };
      }

      // Verify 2FA if provided
      if (requires2FA && twoFactorCode != null) {
        final is2FAValid = await _verifyTwoFactorCode(user['id'], twoFactorCode);
        if (!is2FAValid) {
          return _errorResponse('Invalid two-factor code', 'AUTH_INVALID_2FA');
        }
      }

      // Create full authentication session
      final session = await _createAuthSession(
        user: user,
        organizationId: organizationId,
        rememberMe: rememberMe,
        deviceInfo: deviceInfo,
      );

      await _logAuthEvent(
        userId: user['id'],
        action: 'login_success',
        success: true,
        organizationId: organizationId,
        ipAddress: deviceInfo?['ip'] ?? 'unknown',
        userAgent: deviceInfo?['userAgent'] ?? 'unknown',
      );

      return {
        'success': true,
        'session': session.toJson(),
        'requiresEmailVerification': user['email_verified'] != true,
      };
    } catch (e) {
      print('❌ Authentication error: $e');
      return _errorResponse('Authentication failed', 'AUTH_SYSTEM_ERROR');
    }
  }

  /// Register new user
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String displayName,
    String? firstName,
    String? lastName,
    String? organizationName,
    String? invitationToken,
    Map<String, dynamic>? userPreferences,
    bool acceptTerms = false,
    bool subscribeToNewsletter = false,
    Map<String, String>? deviceInfo,
  }) async {
    try {
      // Validate input
      if (!acceptTerms) {
        return _errorResponse('Terms of service must be accepted', 'AUTH_TERMS_NOT_ACCEPTED');
      }

      // Check if user already exists
      final existingUser = await _findUserByEmail(email);
      if (existingUser != null) {
        return _errorResponse('User already exists', 'AUTH_USER_EXISTS');
      }

      // Validate password strength
      final passwordPolicy = _validatePasswordPolicy(password);
      if (!passwordPolicy['isValid']) {
        return {
          'success': false,
          'error': 'Password does not meet requirements',
          'errorCode': 'AUTH_WEAK_PASSWORD',
          'passwordPolicy': passwordPolicy,
        };
      }

      // Hash password
      final passwordHash = _hashPassword(password);
      final userId = _uuid.v4();
      final now = DateTime.now();

      // Handle organization invitation
      String? organizationId;
      String? roleId;
      if (invitationToken != null) {
        final invitation = await _validateInvitation(invitationToken);
        if (invitation != null) {
          organizationId = invitation['organization_id'];
          roleId = invitation['role_id'];
        }
      }

      // Create user record
      final userData = {
        'id': userId,
        'email': email,
        'password_hash': passwordHash,
        'display_name': displayName,
        'first_name': firstName,
        'last_name': lastName,
        'status': 'pending', // Requires email verification
        'role': 'newcomer',
        'total_points': 0,
        'current_level_points': 0,
        'level': 1,
        'current_streak': 0,
        'longest_streak': 0,
        'achievement_count': 0,
        'quests_completed': 0,
        'tasks_completed': 0,
        'preferences': jsonEncode(userPreferences ?? {}),
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
        'email_verified': false,
        'two_factor_enabled': false,
        'subscribe_newsletter': subscribeToNewsletter,
      };

      await _createUser(userData);

      // Create organization if specified
      if (organizationName != null) {
        organizationId = await _createOrganization(
          name: organizationName,
          ownerId: userId,
        );
      }

      // Add user to organization if specified
      if (organizationId != null) {
        await _addUserToOrganization(
          userId: userId,
          organizationId: organizationId,
          roleId: roleId,
        );
      }

      // Send email verification
      await _sendEmailVerification(userId, email);

      await _logAuthEvent(
        userId: userId,
        action: 'register_success',
        success: true,
        organizationId: organizationId,
        ipAddress: deviceInfo?['ip'] ?? 'unknown',
        userAgent: deviceInfo?['userAgent'] ?? 'unknown',
      );

      return {
        'success': true,
        'userId': userId,
        'requiresEmailVerification': true,
        'organizationCreated': organizationName != null,
        'organizationId': organizationId,
      };
    } catch (e) {
      print('❌ Registration error: $e');
      return _errorResponse('Registration failed', 'AUTH_REGISTRATION_ERROR');
    }
  }

  /// Refresh authentication token
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final session = await _validateRefreshToken(refreshToken);
      if (session == null) {
        return _errorResponse('Invalid refresh token', 'AUTH_INVALID_TOKEN');
      }

      final newSession = await _refreshAuthSession(session);
      return {
        'success': true,
        'session': newSession.toJson(),
      };
    } catch (e) {
      print('❌ Token refresh error: $e');
      return _errorResponse('Token refresh failed', 'AUTH_REFRESH_ERROR');
    }
  }

  /// Verify email address
  Future<Map<String, dynamic>> verifyEmail({
    required String verificationToken,
    required String email,
  }) async {
    try {
      final isValid = await _validateEmailVerificationToken(verificationToken, email);
      if (!isValid) {
        return _errorResponse('Invalid verification token', 'AUTH_INVALID_TOKEN');
      }

      await _markEmailAsVerified(email);
      
      return {
        'success': true,
        'message': 'Email verified successfully',
      };
    } catch (e) {
      print('❌ Email verification error: $e');
      return _errorResponse('Email verification failed', 'AUTH_VERIFICATION_ERROR');
    }
  }

  /// Initiate password reset
  Future<Map<String, dynamic>> initiatePasswordReset(String email) async {
    try {
      final user = await _findUserByEmail(email);
      if (user == null) {
        // Don't reveal if email exists for security
        return {
          'success': true,
          'message': 'If the email exists, a reset link has been sent',
        };
      }

      await _sendPasswordResetEmail(user['id'], email);
      
      return {
        'success': true,
        'message': 'Password reset email sent',
      };
    } catch (e) {
      print('❌ Password reset error: $e');
      return _errorResponse('Password reset failed', 'AUTH_RESET_ERROR');
    }
  }

  /// Complete password reset
  Future<Map<String, dynamic>> completePasswordReset({
    required String resetToken,
    required String newPassword,
  }) async {
    try {
      final userId = await _validatePasswordResetToken(resetToken);
      if (userId == null) {
        return _errorResponse('Invalid or expired reset token', 'AUTH_INVALID_TOKEN');
      }

      // Validate new password
      final passwordPolicy = _validatePasswordPolicy(newPassword);
      if (!passwordPolicy['isValid']) {
        return {
          'success': false,
          'error': 'Password does not meet requirements',
          'errorCode': 'AUTH_WEAK_PASSWORD',
          'passwordPolicy': passwordPolicy,
        };
      }

      await _updateUserPassword(userId, newPassword);
      await _invalidateAllUserSessions(userId);

      return {
        'success': true,
        'message': 'Password updated successfully',
      };
    } catch (e) {
      print('❌ Password reset completion error: $e');
      return _errorResponse('Password reset failed', 'AUTH_RESET_ERROR');
    }
  }

  /// Change password (authenticated user)
  Future<Map<String, dynamic>> changePassword({
    required String userId,
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = await _findUserById(userId);
      if (user == null) {
        return _errorResponse('User not found', 'AUTH_USER_NOT_FOUND');
      }

      // Verify current password
      if (!_verifyPassword(currentPassword, user['password_hash'])) {
        return _errorResponse('Current password is incorrect', 'AUTH_INVALID_PASSWORD');
      }

      // Validate new password
      final passwordPolicy = _validatePasswordPolicy(newPassword);
      if (!passwordPolicy['isValid']) {
        return {
          'success': false,
          'error': 'Password does not meet requirements',
          'errorCode': 'AUTH_WEAK_PASSWORD',
          'passwordPolicy': passwordPolicy,
        };
      }

      await _updateUserPassword(userId, newPassword);
      
      return {
        'success': true,
        'message': 'Password changed successfully',
      };
    } catch (e) {
      print('❌ Password change error: $e');
      return _errorResponse('Password change failed', 'AUTH_CHANGE_ERROR');
    }
  }

  /// Validate authentication session
  Future<AuthSession?> validateSession(String token) async {
    try {
      final payload = _verifyJwtToken(token);
      if (payload == null) return null;

      final userIdFromToken = payload['userId'] as String;
      final sessionId = payload['sessionId'] as String;

      final session = await _findSessionById(sessionId);
      if (session == null) return null;

      // Verify the userId matches the session
      if (session['user_id'] != userIdFromToken) return null;

      // Check if session is expired
      final expiresAt = DateTime.parse(session['expires_at']);
      if (DateTime.now().isAfter(expiresAt)) {
        await _deleteSession(sessionId);
        return null;
      }

      // Update last activity
      await _updateSessionActivity(sessionId);

      return await _buildAuthSessionFromDb(session);
    } catch (e) {
      print('❌ Session validation error: $e');
      return null;
    }
  }

  /// Get password policy requirements
  Map<String, dynamic> getPasswordPolicy() {
    return {
      'minimumLength': 8,
      'requiresUppercase': true,
      'requiresLowercase': true,
      'requiresNumbers': true,
      'requiresSpecialChars': true,
      'specialCharsAllowed': '!@#\$%^&*()_+-=[]{}|;:,.<>?',
    };
  }

  /// Setup two-factor authentication
  Future<Map<String, dynamic>> setupTwoFactor({
    required String userId,
    required TwoFactorMethod method,
    String? phoneNumber,
    String? email,
  }) async {
    try {
      final user = await _findUserById(userId);
      if (user == null) {
        return _errorResponse('User not found', 'AUTH_USER_NOT_FOUND');
      }

      switch (method) {
        case TwoFactorMethod.totp:
          return await _setupTotpTwoFactor(userId);
        case TwoFactorMethod.sms:
          if (phoneNumber == null) {
            return _errorResponse('Phone number required', 'AUTH_PHONE_REQUIRED');
          }
          return await _setupSmsTwoFactor(userId, phoneNumber);
        case TwoFactorMethod.email:
          final userEmail = email ?? user['email'];
          return await _setupEmailTwoFactor(userId, userEmail);
        case TwoFactorMethod.backupCodes:
          return await _generateBackupCodes(userId);
      }
    } catch (e) {
      print('❌ Two-factor setup error: $e');
      return _errorResponse('Two-factor setup failed', 'AUTH_2FA_SETUP_ERROR');
    }
  }

  /// Admin: Impersonate user
  Future<Map<String, dynamic>> impersonateUser({
    required String adminUserId,
    required String targetUserId,
    required String reason,
    int? durationMinutes,
  }) async {
    try {
      // Verify admin has impersonation permission
      final adminUser = await _findUserById(adminUserId);
      if (adminUser == null) {
        return _errorResponse('Admin user not found', 'AUTH_ADMIN_NOT_FOUND');
      }

      final hasPermission = await _checkSystemPermission(
        adminUserId,
        SystemPermission.userImpersonation,
      );
      if (!hasPermission) {
        return _errorResponse('Insufficient permissions', 'AUTH_INSUFFICIENT_PERMISSIONS');
      }

      final targetUser = await _findUserById(targetUserId);
      if (targetUser == null) {
        return _errorResponse('Target user not found', 'AUTH_TARGET_NOT_FOUND');
      }

      // Create impersonation session
      final session = await _createImpersonationSession(
        adminUserId: adminUserId,
        targetUserId: targetUserId,
        reason: reason,
        durationMinutes: durationMinutes,
      );

      await _logAuthEvent(
        userId: adminUserId,
        action: 'user_impersonation',
        success: true,
        metadata: {
          'target_user_id': targetUserId,
          'reason': reason,
          'duration_minutes': durationMinutes,
        },
        ipAddress: 'admin_action',
        userAgent: 'admin_panel',
      );

      return {
        'success': true,
        'session': session.toJson(),
        'impersonationDetails': {
          'adminUserId': adminUserId,
          'targetUserId': targetUserId,
          'reason': reason,
          'expiresAt': durationMinutes != null
              ? DateTime.now().add(Duration(minutes: durationMinutes)).toIso8601String()
              : null,
        },
      };
    } catch (e) {
      print('❌ User impersonation error: $e');
      return _errorResponse('Impersonation failed', 'AUTH_IMPERSONATION_ERROR');
    }
  }

  /// Logout user (invalidate session)
  Future<Map<String, dynamic>> logout(String sessionId) async {
    try {
      await _deleteSession(sessionId);
      return {
        'success': true,
        'message': 'Logged out successfully',
      };
    } catch (e) {
      print('❌ Logout error: $e');
      return _errorResponse('Logout failed', 'AUTH_LOGOUT_ERROR');
    }
  }

  // Private helper methods

  Future<Map<String, dynamic>?> _findUserByEmail(String email) async {
    try {
      final result = await _dbService.execute('''
        SELECT id, username, email, display_name, password_hash,
               first_name, last_name, avatar_url, created_at, updated_at,
               status::text, email_verified, two_factor_enabled, is_active
        FROM quester.users 
        WHERE email = @email
      ''', parameters: {'email': email});
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'id': row[0],
        'username': row[1],
        'email': row[2],
        'display_name': row[3],
        'password_hash': row[4],
        'first_name': row[5],
        'last_name': row[6],
        'avatar_url': row[7],
        'created_at': row[8],
        'updated_at': row[9],
        'status': row[10],
        'email_verified': row[11],
        'two_factor_enabled': row[12] ?? false,
        'is_active': row[13] ?? true,
      };
    } catch (e) {
      print('❌ Error finding user by email: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> _findUserById(String userId) async {
    try {
      final result = await _dbService.execute('''
        SELECT id, username, email, display_name, password_hash,
               first_name, last_name, avatar_url, created_at, updated_at,
               status::text, email_verified, two_factor_enabled, is_active
        FROM quester.users 
        WHERE id = @userId::uuid AND is_active = true
      ''', parameters: {'userId': userId});
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'id': row[0],
        'username': row[1],
        'email': row[2],
        'display_name': row[3],
        'password_hash': row[4],
        'first_name': row[5],
        'last_name': row[6],
        'avatar_url': row[7],
        'created_at': row[8],
        'updated_at': row[9],
        'status': row[10],
        'email_verified': row[11],
        'two_factor_enabled': row[12] ?? false,
        'is_active': row[13] ?? true,
      };
    } catch (e) {
      print('❌ Error finding user by ID: $e');
      return null;
    }
  }

  String _hashPassword(String password) {
    // Use proper PBKDF2 with random salt for security
    final salt = _generateSalt();
    return '$salt:${_hashWithSalt(password, salt)}';
  }

  bool _verifyPassword(String password, String hashedPassword) {
    final parts = hashedPassword.split(':');
    if (parts.length != 2) return false;
    
    final salt = parts[0];
    final hash = parts[1];
    
    return _hashWithSalt(password, salt) == hash;
  }

  String _hashWithSalt(String password, String salt) {
    // Improved PBKDF2 implementation with proper iterations
    final bytes = utf8.encode(password + salt);
    var digest = sha256.convert(bytes);
    
    // Apply 100,000 iterations for strong security (OWASP recommended)
    for (int i = 0; i < 100000; i++) {
      digest = sha256.convert(digest.bytes + bytes);
    }
    
    return digest.toString();
  }

  String _generateSalt() {
    final bytes = List<int>.generate(16, (i) => _random.nextInt(256));
    return base64.encode(bytes);
  }

  Map<String, dynamic> _validatePasswordPolicy(String password) {
    final policy = getPasswordPolicy();
    final failedRequirements = <String>[];
    
    if (password.length < policy['minimumLength']) {
      failedRequirements.add('Password must be at least ${policy['minimumLength']} characters long');
    }
    
    if (policy['requiresUppercase'] && !password.contains(RegExp(r'[A-Z]'))) {
      failedRequirements.add('Password must contain at least one uppercase letter');
    }
    
    if (policy['requiresLowercase'] && !password.contains(RegExp(r'[a-z]'))) {
      failedRequirements.add('Password must contain at least one lowercase letter');
    }
    
    if (policy['requiresNumbers'] && !password.contains(RegExp(r'[0-9]'))) {
      failedRequirements.add('Password must contain at least one number');
    }
    
    if (policy['requiresSpecialChars'] && !password.contains(RegExp(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]'))) {
      failedRequirements.add('Password must contain at least one special character');
    }
    
    // Calculate strength score (0-100)
    int strengthScore = 0;
    if (password.length >= 8) strengthScore += 20;
    if (password.length >= 12) strengthScore += 10;
    if (password.contains(RegExp(r'[A-Z]'))) strengthScore += 15;
    if (password.contains(RegExp(r'[a-z]'))) strengthScore += 15;
    if (password.contains(RegExp(r'[0-9]'))) strengthScore += 15;
    if (password.contains(RegExp(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]'))) strengthScore += 15;
    if (password.length >= 16) strengthScore += 10;
    
    return {
      'isValid': failedRequirements.isEmpty,
      'failedRequirements': failedRequirements,
      'strengthScore': strengthScore,
      ...policy,
    };
  }

  Future<AuthSession> _createAuthSession({
    required Map<String, dynamic> user,
    String? organizationId,
    bool rememberMe = false,
    Map<String, String>? deviceInfo,
  }) async {
    final sessionId = _uuid.v4();
    final now = DateTime.now();
    final expiresAt = now.add(Duration(
      hours: rememberMe ? 24 * 30 : _accessTokenExpiryHours,
    ));

    // Create JWT token
    final token = _generateJwtToken({
      'userId': user['id'],
      'sessionId': sessionId,
      'email': user['email'],
      'role': user['role'] ?? 'user', // Default role if not set
      'iat': now.millisecondsSinceEpoch ~/ 1000,
      'exp': expiresAt.millisecondsSinceEpoch ~/ 1000,
    });

    final refreshToken = _generateRefreshToken();

    // Store session in database
    await _storeSession({
      'id': sessionId,
      'user_id': user['id'],
      'token_hash': _hashToken(token),
      'refresh_token': refreshToken,
      'expires_at': expiresAt.toIso8601String(),
      'created_at': now.toIso8601String(),
      'last_activity': now.toIso8601String(),
      'device_info': jsonEncode(deviceInfo ?? {}),
      'is_active': true,
    });

    // Get user's organization roles
    final organizationRoles = await _getUserOrganizationRoles(user['id']);

    // Create a minimal User object for authentication
    final authUser = User(
      id: user['id'] as String,
      email: user['email'] as String,
      displayName: user['display_name'] as String? ?? 'User',
      firstName: user['first_name'] as String?,
      lastName: user['last_name'] as String?,
      avatarUrl: user['avatar_url'] as String?,
      role: UserRole.newcomer, // Default role for new users
      status: UserStatus.active, // For authenticated users
      totalPoints: 0, // Default gamification values
      currentLevelPoints: 0,
      level: 1,
      currentStreak: 0,
      longestStreak: 0,
      achievementCount: 0,
      questsCompleted: 0,
      tasksCompleted: 0,
      preferences: null,
      createdAt: user['created_at'] is String 
        ? DateTime.parse(user['created_at'] as String) 
        : user['created_at'] as DateTime? ?? DateTime.now(),
      updatedAt: user['updated_at'] is String 
        ? DateTime.parse(user['updated_at'] as String) 
        : user['updated_at'] as DateTime? ?? DateTime.now(),
      lastLoginAt: null,
    );

    return AuthSession(
      token: token,
      refreshToken: refreshToken,
      expiresAt: expiresAt,
      user: authUser,
      organizationRoles: organizationRoles,
      createdAt: now,
      lastActivityAt: now,
    );
  }

  String _generateJwtToken(Map<String, dynamic> payload) {
    // Simplified JWT generation - use proper library in production
    final header = base64Url.encode(utf8.encode(jsonEncode({
      'typ': 'JWT',
      'alg': 'HS256',
    })));
    
    final payloadEncoded = base64Url.encode(utf8.encode(jsonEncode(payload)));
    
    final signature = base64Url.encode(
      Hmac(sha256, utf8.encode(_jwtSecret))
          .convert(utf8.encode('$header.$payloadEncoded'))
          .bytes,
    );
    
    return '$header.$payloadEncoded.$signature';
  }

  Map<String, dynamic>? _verifyJwtToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;
      
      final header = parts[0];
      final payload = parts[1];
      final signature = parts[2];
      
      // Verify signature
      final expectedSignature = base64Url.encode(
        Hmac(sha256, utf8.encode(_jwtSecret))
            .convert(utf8.encode('$header.$payload'))
            .bytes,
      );
      
      if (signature != expectedSignature) return null;
      
      // Decode payload
      final decodedPayload = jsonDecode(
        utf8.decode(base64Url.decode(payload)),
      ) as Map<String, dynamic>;
      
      // Check expiration
      final exp = decodedPayload['exp'] as int;
      if (DateTime.now().millisecondsSinceEpoch ~/ 1000 > exp) {
        return null;
      }
      
      return decodedPayload;
    } catch (e) {
      return null;
    }
  }

  String _generateRefreshToken() {
    final bytes = List<int>.generate(32, (i) => _random.nextInt(256));
    return base64Url.encode(bytes);
  }

  String _hashToken(String token) {
    return sha256.convert(utf8.encode(token)).toString();
  }

  Future<void> _logAuthEvent({
    required String userId,
    required String action,
    required bool success,
    String? organizationId,
    Map<String, dynamic>? metadata,
    required String ipAddress,
    required String userAgent,
    String? errorMessage,
    String? sessionId,
  }) async {
    await _auditService.logAuthEvent(
      userId: userId,
      action: action,
      success: success,
      organizationId: organizationId,
      metadata: metadata,
      ipAddress: ipAddress,
      userAgent: userAgent,
      errorMessage: errorMessage,
      sessionId: sessionId,
    );
  }

  Map<String, dynamic> _errorResponse(String message, String errorCode) {
    return {
      'success': false,
      'error': message,
      'errorCode': errorCode,
    };
  }

  // Database operations
  Future<void> _createUser(Map<String, dynamic> userData) async {
    try {
      // Insert user with proper schema reference and error handling
      await _dbService.execute('''
        INSERT INTO quester.users (
          id, username, email, display_name, password_hash,
          first_name, last_name, avatar_url,
          is_active, email_verified, two_factor_enabled,
          created_at, updated_at
        ) VALUES (
          @id::uuid, @username, @email, @display_name, @password_hash,
          @first_name, @last_name, @avatar_url,
          @is_active, @email_verified, @two_factor_enabled,
          @created_at::timestamptz, @updated_at::timestamptz
        )
      ''', parameters: {
        'id': userData['id'],
        'username': userData['email'], // Use email as username for now
        'email': userData['email'],
        'display_name': userData['display_name'],
        'password_hash': userData['password_hash'],
        'first_name': userData['first_name'],
        'last_name': userData['last_name'],
        'avatar_url': null,
        'is_active': true,
        'email_verified': userData['email_verified'] ?? false,
        'two_factor_enabled': userData['two_factor_enabled'] ?? false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('❌ Error creating user: $e');
      rethrow;
    }
  }

  Future<String?> _createOrganization({
    required String name,
    required String ownerId,
  }) async {
    try {
      final organizationId = _uuid.v4();
      final slug = _generateSlug(name);
      final now = DateTime.now().toUtc();

      // Create organization
      await _dbService.connection!.execute(
        '''
          INSERT INTO organizations (
            id, name, slug, description, subscription_plan,
            max_users, max_quests, is_active, created_at, updated_at
          ) VALUES (
            \$1, \$2, \$3, \$4, \$5,
            \$6, \$7, \$8, \$9, \$10
          )
        ''',
        parameters: [
          organizationId,
          name,
          slug,
          'Organization created for $name',
          'free',
          10,
          50,
          true,
          now,
          now,
        ],
      );

      // Create default roles for the organization
      await _createDefaultRoles(organizationId);

      // Add owner as admin member
      await _addUserToOrganization(
        userId: ownerId,
        organizationId: organizationId,
        role: 'admin',
      );

      print('✅ Organization created: $name (ID: $organizationId)');
      return organizationId;

    } catch (e) {
      print('❌ Error creating organization: $e');
      return null;
    }
  }

  Future<void> _addUserToOrganization({
    required String userId,
    required String organizationId,
    String? roleId,
    String? role,
  }) async {
    try {
      final now = DateTime.now().toUtc();
      String finalRole = role ?? 'member';

      // If roleId is provided, get the role name
      if (roleId != null) {
        final roleResult = await _dbService.connection!.execute(
          'SELECT name FROM organization_roles WHERE id = \$1',
          parameters: [roleId],
        );
        if (roleResult.isNotEmpty) {
          finalRole = roleResult.first[0] as String;
        }
      }

      // Add user to organization
      await _dbService.connection!.execute(
        '''
          INSERT INTO organization_members (
            id, organization_id, user_id, role, joined_at, is_active, created_at, updated_at
          ) VALUES (
            \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8
          )
          ON CONFLICT (organization_id, user_id)
          DO UPDATE SET
            role = EXCLUDED.role,
            is_active = EXCLUDED.is_active,
            updated_at = EXCLUDED.updated_at
        ''',
        parameters: [
          _uuid.v4(),
          organizationId,
          userId,
          finalRole,
          now,
          true,
          now,
          now,
        ],
      );

      print('✅ User $userId added to organization $organizationId with role: $finalRole');
    } catch (e) {
      print('❌ Error adding user to organization: $e');
      rethrow;
    }
  }

  Future<void> _sendEmailVerification(String userId, String email) async {
    try {
      final verificationToken = _generateRefreshToken();
      final expiresAt = DateTime.now().add(Duration(hours: 24));
      
      // Store verification token
      await _dbService.execute('''
        INSERT INTO quester.email_verification_tokens 
        (user_id, token, email, expires_at, created_at)
        VALUES (@user_id::uuid, @token, @email, @expires_at::timestamptz, @created_at::timestamptz)
      ''', parameters: {
        'user_id': userId,
        'token': verificationToken,
        'email': email,
        'expires_at': expiresAt.toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      });
      
      // TODO: Integrate with email service
      // In a real implementation, this would send an email with the verification link
      // Example: await _emailService.sendVerificationEmail(email, token);
      LoggingService.email('Email verification token generated for $email', recipient: email);
    } catch (e) {
      LoggingService.error('Error creating email verification token', tag: 'AuthService', error: e);
      // Don't rethrow as this shouldn't block user registration
    }
  }

  Future<void> _storeSession(Map<String, dynamic> sessionData) async {
    try {
      await _dbService.execute('''
        INSERT INTO quester.user_sessions_enhanced (
          id, user_id, session_token_hash, refresh_token_hash,
          ip_address, user_agent, device_info, expires_at, absolute_expires_at,
          last_activity, is_active, created_at
        ) VALUES (
          @id::uuid, @user_id::uuid, @token_hash, @refresh_token,
          @ip_address, @user_agent, @device_info::jsonb, @expires_at::timestamptz, @absolute_expires_at::timestamptz,
          @last_activity::timestamptz, @is_active, @created_at::timestamptz
        )
      ''', parameters: {
        'id': sessionData['id'],
        'user_id': sessionData['user_id'],
        'token_hash': sessionData['token_hash'],
        'refresh_token': sessionData['refresh_token'],
        'ip_address': _sanitizeIpAddress(sessionData['ip_address']),
        'user_agent': sessionData['user_agent'] ?? 'unknown',
        'device_info': sessionData['device_info'] ?? '{}',
        'expires_at': sessionData['expires_at'],
        'absolute_expires_at': DateTime.parse(sessionData['expires_at']).add(Duration(days: 30)).toIso8601String(),
        'last_activity': sessionData['last_activity'],
        'is_active': sessionData['is_active'],
        'created_at': sessionData['created_at'],
      });
    } catch (e) {
      print('❌ Error storing session: $e');
      rethrow;
    }
  }

  Future<List<UserOrganizationRole>> _getUserOrganizationRoles(String userId) async {
    try {
      final result = await _dbService.connection!.execute(
        '''
          SELECT
            om.organization_id,
            om.role,
            o.name as organization_name,
            or_role.permissions
          FROM organization_members om
          JOIN organizations o ON om.organization_id = o.id
          LEFT JOIN organization_roles or_role ON (or_role.organization_id = om.organization_id AND or_role.name = om.role)
          WHERE om.user_id = \$1 AND om.is_active = true AND o.is_active = true
        ''',
        parameters: [userId],
      );

      return result.map((row) {
        final permissions = row[3] != null
            ? Map<String, dynamic>.from(jsonDecode(row[3] as String))
            : <String, dynamic>{};

        // Create OrganizationRole object
        final organizationRole = OrganizationRole(
          id: '${row[1]}-${row[0]}', // role-organizationId
          organizationId: row[0] as String,
          name: row[1] as String,
          description: 'Organization ${row[1]} role',
          isSystemRole: true,
          isActive: true,
          permissions: _convertPermissionsToSet(permissions),
          priority: row[1] == 'admin' ? 900 : 700,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        return UserOrganizationRole(
          organizationId: row[0] as String,
          organizationName: row[2] as String,
          role: organizationRole,
          isDefault: row[3] as bool? ?? false, // Get default flag from database
          assignedAt: DateTime.tryParse(row[4] as String? ?? '') ?? DateTime.now(), // Get assignment date from database
        );
      }).toList();
    } catch (e) {
      print('❌ Error fetching user organization roles: $e');
      return [];
    }
  }

  /// Sanitize IP address for database storage
  String _sanitizeIpAddress(String? ipAddress) {
    if (ipAddress == null || ipAddress.isEmpty || ipAddress == 'unknown') {
      return '127.0.0.1'; // Default to localhost
    }
    
    // Basic IP address validation
    try {
      final parts = ipAddress.split('.');
      if (parts.length == 4) {
        for (final part in parts) {
          final num = int.parse(part);
          if (num < 0 || num > 255) {
            return '127.0.0.1';
          }
        }
        return ipAddress;
      }
    } catch (e) {
      // Invalid IP format
    }
    
    // Check for IPv6 (basic validation)
    if (ipAddress.contains(':') && ipAddress.length > 7) {
      return ipAddress; // Assume valid IPv6
    }
    
    return '127.0.0.1'; // Default fallback
  }

  Future<Map<String, dynamic>?> _findSessionById(String sessionId) async {
    try {
      final result = await _dbService.execute('''
        SELECT id, user_id, session_token_hash, refresh_token_hash,
               expires_at, absolute_expires_at, last_activity, is_active,
               ip_address, user_agent, device_info
        FROM quester.user_sessions_enhanced
        WHERE id = @sessionId::uuid AND is_active = true
      ''', parameters: {'sessionId': sessionId});
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      return {
        'id': row[0],
        'user_id': row[1],
        'session_token_hash': row[2],
        'refresh_token_hash': row[3],
        'expires_at': row[4],
        'absolute_expires_at': row[5],
        'last_activity': row[6],
        'is_active': row[7],
        'ip_address': row[8],
        'user_agent': row[9],
        'device_info': row[10],
      };
    } catch (e) {
      print('❌ Error finding session: $e');
      return null;
    }
  }

  Future<void> _deleteSession(String sessionId) async {
    try {
      await _dbService.execute('''
        UPDATE quester.user_sessions_enhanced 
        SET is_active = false, updated_at = NOW()
        WHERE id = @sessionId::uuid
      ''', parameters: {'sessionId': sessionId});
    } catch (e) {
      print('❌ Error deleting session: $e');
      rethrow;
    }
  }

  Future<void> _updateSessionActivity(String sessionId) async {
    try {
      await _dbService.execute('''
        UPDATE quester.user_sessions_enhanced 
        SET last_activity = NOW(), updated_at = NOW()
        WHERE id = @sessionId::uuid AND is_active = true
      ''', parameters: {'sessionId': sessionId});
    } catch (e) {
      print('❌ Error updating session activity: $e');
    }
  }

  Future<AuthSession> _buildAuthSessionFromDb(Map<String, dynamic> session) async {
    try {
      // Get user data
      final user = await _findUserById(session['user_id']);
      if (user == null) {
        throw Exception('User not found for session');
      }

      // Get organization roles
      final organizationRoles = await _getUserOrganizationRoles(session['user_id']);

      // Create User object
      final authUser = User(
        id: user['id'] as String,
        email: user['email'] as String,
        displayName: user['display_name'] as String? ?? 'User',
        firstName: user['first_name'] as String?,
        lastName: user['last_name'] as String?,
        avatarUrl: user['avatar_url'] as String?,
        role: UserRole.newcomer,
        status: UserStatus.active,
        totalPoints: 0,
        currentLevelPoints: 0,
        level: 1,
        currentStreak: 0,
        longestStreak: 0,
        achievementCount: 0,
        questsCompleted: 0,
        tasksCompleted: 0,
        preferences: null,
        createdAt: user['created_at'] is String 
          ? DateTime.parse(user['created_at'] as String) 
          : user['created_at'] as DateTime? ?? DateTime.now(),
        updatedAt: user['updated_at'] is String 
          ? DateTime.parse(user['updated_at'] as String) 
          : user['updated_at'] as DateTime? ?? DateTime.now(),
        lastLoginAt: null,
      );

      // Generate new tokens (refresh the session)
      final token = _generateJwtToken({
        'userId': user['id'],
        'sessionId': session['id'],
        'email': user['email'],
        'role': user['role'] ?? 'user',
        'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'exp': DateTime.parse(session['expires_at']).millisecondsSinceEpoch ~/ 1000,
      });

      return AuthSession(
        token: token,
        refreshToken: session['refresh_token_hash'],
        expiresAt: DateTime.parse(session['expires_at']),
        user: authUser,
        organizationRoles: organizationRoles,
        createdAt: DateTime.now(),
        lastActivityAt: DateTime.parse(session['last_activity']),
      );
    } catch (e) {
      print('❌ Error building auth session from DB: $e');
      rethrow;
    }
  }

  Future<AuthSession?> _validateRefreshToken(String refreshToken) async {
    try {
      final result = await _dbService.execute('''
        SELECT id, user_id, session_token_hash, expires_at, absolute_expires_at,
               last_activity, is_active, ip_address, user_agent, device_info
        FROM quester.user_sessions_enhanced
        WHERE refresh_token_hash = @refreshToken AND is_active = true
        AND absolute_expires_at > NOW()
      ''', parameters: {'refreshToken': refreshToken});
      
      if (result.isEmpty) return null;
      
      final row = result.first;
      final sessionData = {
        'id': row[0],
        'user_id': row[1],
        'session_token_hash': row[2],
        'expires_at': row[3].toString(),
        'absolute_expires_at': row[4].toString(),
        'last_activity': row[5].toString(),
        'is_active': row[6],
        'ip_address': row[7],
        'user_agent': row[8],
        'device_info': row[9],
        'refresh_token_hash': refreshToken,
      };

      return await _buildAuthSessionFromDb(sessionData);
    } catch (e) {
      print('❌ Error validating refresh token: $e');
      return null;
    }
  }

  Future<AuthSession> _refreshAuthSession(AuthSession session) async {
    try {
      final now = DateTime.now();
      final expiresAt = now.add(Duration(hours: _accessTokenExpiryHours));
      
      // Generate new token
      final token = _generateJwtToken({
        'userId': session.user.id,
        'sessionId': _uuid.v4(), // This should be extracted from session metadata
        'email': session.user.email,
        'role': 'user', // Should be from user data
        'iat': now.millisecondsSinceEpoch ~/ 1000,
        'exp': expiresAt.millisecondsSinceEpoch ~/ 1000,
      });

      // Update session in database
      await _updateSessionActivity(_uuid.v4()); // Should use actual session ID

      return session.copyWith(
        token: token,
        expiresAt: expiresAt,
        lastActivityAt: now,
      );
    } catch (e) {
      print('❌ Error refreshing auth session: $e');
      rethrow;
    }
  }

  Future<bool> _validateEmailVerificationToken(String token, String email) async {
    try {
      final result = await _dbService.execute('''
        SELECT user_id, expires_at FROM quester.email_verification_tokens 
        WHERE token = @token AND email = @email AND used_at IS NULL
      ''', parameters: {'token': token, 'email': email});
      
      if (result.isEmpty) return false;
      
      final expiresAt = DateTime.parse(result.first[1] as String);
      return DateTime.now().isBefore(expiresAt);
    } catch (e) {
      print('❌ Error validating email verification token: $e');
      return false;
    }
  }

  Future<void> _markEmailAsVerified(String email) async {
    try {
      await _dbService.execute('''
        UPDATE quester.users 
        SET email_verified = true, updated_at = NOW()
        WHERE email = @email
      ''', parameters: {'email': email});
      
      // Mark verification token as used
      await _dbService.execute('''
        UPDATE quester.email_verification_tokens 
        SET used_at = NOW() 
        WHERE email = @email AND used_at IS NULL
      ''', parameters: {'email': email});
    } catch (e) {
      print('❌ Error marking email as verified: $e');
      rethrow;
    }
  }

  Future<void> _sendPasswordResetEmail(String userId, String email) async {
    try {
      final resetToken = _generateRefreshToken();
      final expiresAt = DateTime.now().add(Duration(hours: 1));
      
      // Store reset token
      await _dbService.execute('''
        INSERT INTO quester.password_reset_tokens 
        (user_id, token, email, expires_at, created_at)
        VALUES (@user_id::uuid, @token, @email, @expires_at::timestamptz, @created_at::timestamptz)
      ''', parameters: {
        'user_id': userId,
        'token': resetToken,
        'email': email,
        'expires_at': expiresAt.toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      });
      
      // TODO: Integrate with email service
      // In a real implementation, this would send a password reset email
      // Example: await _emailService.sendPasswordResetEmail(email, token);
      LoggingService.email('Password reset token generated for $email', recipient: email);
    } catch (e) {
      LoggingService.error('Error creating password reset token', tag: 'AuthService', error: e);
      rethrow;
    }
  }

  Future<String?> _validatePasswordResetToken(String token) async {
    try {
      final result = await _dbService.execute('''
        SELECT user_id, expires_at FROM quester.password_reset_tokens 
        WHERE token = @token AND used_at IS NULL
      ''', parameters: {'token': token});
      
      if (result.isEmpty) return null;
      
      final userId = result.first[0] as String;
      final expiresAt = DateTime.parse(result.first[1] as String);
      
      if (DateTime.now().isAfter(expiresAt)) {
        // Token expired, clean it up
        await _dbService.execute('''
          DELETE FROM quester.password_reset_tokens WHERE token = @token
        ''', parameters: {'token': token});
        return null;
      }
      
      return userId;
    } catch (e) {
      print('❌ Error validating password reset token: $e');
      return null;
    }
  }

  Future<void> _updateUserPassword(String userId, String newPassword) async {
    try {
      final hashedPassword = _hashPassword(newPassword);
      
      await _dbService.execute('''
        UPDATE quester.users 
        SET password_hash = @password_hash, updated_at = NOW()
        WHERE id = @user_id::uuid
      ''', parameters: {
        'user_id': userId,
        'password_hash': hashedPassword,
      });
      
      // Mark reset token as used if exists
      await _dbService.execute('''
        UPDATE quester.password_reset_tokens 
        SET used_at = NOW() 
        WHERE user_id = @user_id::uuid AND used_at IS NULL
      ''', parameters: {'user_id': userId});
    } catch (e) {
      print('❌ Error updating user password: $e');
      rethrow;
    }
  }

  Future<void> _invalidateAllUserSessions(String userId) async {
    try {
      await _dbService.execute('''
        UPDATE quester.user_sessions_enhanced 
        SET is_active = false, updated_at = NOW()
        WHERE user_id = @user_id::uuid AND is_active = true
      ''', parameters: {'user_id': userId});
    } catch (e) {
      print('❌ Error invalidating user sessions: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> _validateInvitation(String token) async {
    try {
      final result = await _dbService.connection!.execute(
        '''
          SELECT
            it.organization_id,
            it.invited_email,
            it.role_id,
            it.expires_at,
            it.used_at,
            o.name as organization_name,
            or_role.name as role_name
          FROM invitation_tokens it
          JOIN organizations o ON it.organization_id = o.id
          LEFT JOIN organization_roles or_role ON it.role_id = or_role.id
          WHERE it.token = \$1 AND it.used_at IS NULL
        ''',
        parameters: [token],
      );

      if (result.isEmpty) {
        return null; // Invalid or already used token
      }

      final row = result.first;
      final expiresAt = DateTime.parse(row[3] as String);

      if (expiresAt.isBefore(DateTime.now().toUtc())) {
        return null; // Expired token
      }

      return {
        'organization_id': row[0] as String,
        'invited_email': row[1] as String,
        'role_id': row[2] as String?,
        'organization_name': row[5] as String,
        'role_name': row[6] as String? ?? 'member',
        'expires_at': expiresAt.toIso8601String(),
      };
    } catch (e) {
      print('❌ Error validating invitation: $e');
      return null;
    }
  }

  Future<String> _createTwoFactorSession(String userId) async {
    try {
      // Create a temporary 2FA session token
      final sessionToken = _generateRefreshToken();

      // Store the 2FA session in database with expiration
      // In a real implementation, this would store the session in the database
      // For now, we'll just generate and return the token
      LoggingService.info('2FA session created for user $userId', tag: 'AuthService');

      return sessionToken;
    } catch (e) {
      LoggingService.error('Error creating 2FA session', tag: 'AuthService', error: e);
      rethrow;
    }
  }

  Future<List<TwoFactorMethod>> _getTwoFactorMethods(String userId) async {
    try {
      return await _mfaService.getAvailableMethods(userId);
    } catch (e) {
      print('❌ Error getting 2FA methods: $e');
      return [];
    }
  }

  Future<bool> _verifyTwoFactorCode(String userId, String code) async {
    try {
      // Get available MFA methods for user
      final availableMethods = await _mfaService.getAvailableMethods(userId);
      
      // Try to verify with each available method
      for (final method in availableMethods) {
        final isValid = await _mfaService.verifyMFACode(userId, code, method);
        if (isValid) {
          return true;
        }
      }
      
      return false;
    } catch (e) {
      print('❌ Error verifying 2FA code: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>> _setupTotpTwoFactor(String userId) async {
    return await _mfaService.setupTOTP(userId);
  }

  Future<Map<String, dynamic>> _setupSmsTwoFactor(String userId, String phoneNumber) async {
    return await _mfaService.setupSMS(userId, phoneNumber);
  }

  Future<Map<String, dynamic>> _setupEmailTwoFactor(String userId, String email) async {
    return await _mfaService.setupEmail(userId, email);
  }

  Future<Map<String, dynamic>> _generateBackupCodes(String userId) async {
    // This will be handled by the MFA service setup methods
    return {'success': true, 'backupCodes': []};
  }

  Future<bool> _checkSystemPermission(String userId, SystemPermission permission) async {
    try {
      // Check user's system permissions based on their role
      // In a real implementation, this would query the database for user roles and permissions
      // Example: SELECT permissions FROM user_roles WHERE user_id = ? AND role_id = ?

      // For now, return true for all permissions (development mode)
      // In production, this would check against user_roles and role_permissions tables
      LoggingService.info('Checking permission ${permission.toString()} for user $userId', tag: 'AuthService');
      return true;
    } catch (e) {
      LoggingService.error('Error checking system permission', tag: 'AuthService', error: e);
      return false;
    }
  }

  Future<User?> _getUserById(String userId) async {
    try {
      final result = await _dbService.connection!.execute(
        '''
          SELECT
            id, email, display_name, avatar_url, is_active,
            created_at, updated_at, last_login_at, email_verified_at
          FROM users
          WHERE id = \$1 AND is_active = true
        ''',
        parameters: [userId],
      );

      if (result.isEmpty) {
        return null;
      }

      final row = result.first;
      return User(
        id: row[0] as String,
        email: row[1] as String,
        displayName: row[2] as String? ?? 'User',
        firstName: row.length > 7 ? (row[7] as String? ?? 'User') : 'User', // Get from first_name column if available
        lastName: row.length > 8 ? (row[8] as String? ?? '') : '', // Get from last_name column if available
        role: _parseUserRole(row.length > 9 ? row[9] as String? : null), // Get role from database
        status: (row[4] as bool) ? UserStatus.active : UserStatus.inactive,
        totalPoints: row.length > 10 ? (row[10] as int? ?? 0) : 0, // Get from user_points table
        currentLevelPoints: row.length > 11 ? (row[11] as int? ?? 0) : 0, // Calculate from user_points table
        level: row.length > 12 ? (row[12] as int? ?? 1) : 1, // Get from user_points table
        currentStreak: row.length > 13 ? (row[13] as int? ?? 0) : 0, // Get from streaks table
        longestStreak: row.length > 14 ? (row[14] as int? ?? 0) : 0, // Get from streaks table
        achievementCount: row.length > 15 ? (row[15] as int? ?? 0) : 0, // Count from user_achievements table
        questsCompleted: row.length > 16 ? (row[16] as int? ?? 0) : 0, // Count from quests table
        tasksCompleted: row.length > 17 ? (row[17] as int? ?? 0) : 0, // Count from tasks table
        preferences: row.length > 18 ? _parseUserPreferences(row[18] as String?) : {}, // Get from user preferences
        createdAt: DateTime.parse(row[5] as String),
        updatedAt: DateTime.parse(row[6] as String),
      );
    } catch (e) {
      print('Error fetching user $userId: $e');
      return null;
    }
  }

  Future<AuthSession> _createImpersonationSession({
    required String adminUserId,
    required String targetUserId,
    required String reason,
    int? durationMinutes,
  }) async {
    // Validate admin has impersonation permission
    final hasPermission = await _checkSystemPermission(adminUserId, SystemPermission.userImpersonation);
    if (!hasPermission) {
      throw Exception('Admin user does not have impersonation permission');
    }
    
    // Get target user details
    final targetUser = await _getUserById(targetUserId);
    if (targetUser == null) {
      throw Exception('Target user not found');
    }
    
    // Create impersonation session
    final sessionId = _uuid.v4();
    final now = DateTime.now();
    final duration = durationMinutes ?? 60; // Default 1 hour
    final expiresAt = now.add(Duration(minutes: duration));
    
    final session = AuthSession(
      token: 'impersonation_${sessionId.substring(0, 16)}',
      refreshToken: 'refresh_${sessionId.substring(16)}',
      expiresAt: expiresAt,
      user: targetUser,
      organizationRoles: [], // Empty for basic impersonation
      metadata: {
        'type': 'impersonation',
        'admin_user_id': adminUserId,
        'reason': reason,
        'duration_minutes': duration,
        'created_by': 'system',
      },
      createdAt: now,
      lastActivityAt: now,
    );
    
    // Store session for tracking
    _activeSessions[sessionId] = session;
    
    // Log the impersonation event for audit
    await _auditService.logAuthEvent(
      userId: adminUserId,
      action: 'user_impersonation_started',
      success: true,
      metadata: {
        'target_user_id': targetUserId,
        'reason': reason,
        'duration_minutes': duration,
      },
      ipAddress: '127.0.0.1',
      userAgent: 'AdminImpersonation/1.0',
      sessionId: sessionId,
    );
    
    return session;
  }

  /// Generate a URL-friendly slug from organization name
  String _generateSlug(String name) {
    return name
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '-')
        .replaceAll(RegExp(r'-+'), '-')
        .replaceAll(RegExp(r'^-|-$'), '');
  }

  /// Create default roles for a new organization
  Future<void> _createDefaultRoles(String organizationId) async {
    try {
      final now = DateTime.now().toUtc();

      // Create admin role
      await _dbService.connection!.execute(
        '''
          INSERT INTO organization_roles (
            id, organization_id, name, description, permissions, is_system_role, created_at, updated_at
          ) VALUES (
            \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8
          )
        ''',
        parameters: [
          _uuid.v4(),
          organizationId,
          'admin',
          'Organization Administrator',
          jsonEncode({
            'manage_organization': true,
            'manage_users': true,
            'manage_quests': true,
            'view_analytics': true,
            'manage_roles': true,
          }),
          true,
          now,
          now,
        ],
      );

      // Create member role
      await _dbService.connection!.execute(
        '''
          INSERT INTO organization_roles (
            id, organization_id, name, description, permissions, is_system_role, created_at, updated_at
          ) VALUES (
            \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8
          )
        ''',
        parameters: [
          _uuid.v4(),
          organizationId,
          'member',
          'Organization Member',
          jsonEncode({
            'view_quests': true,
            'participate': true,
            'view_profile': true,
          }),
          true,
          now,
          now,
        ],
      );

      print('✅ Default roles created for organization: $organizationId');
    } catch (e) {
      print('❌ Error creating default roles: $e');
      rethrow;
    }
  }

  /// Convert permissions map to Permission enum set
  Set<Permission> _convertPermissionsToSet(Map<String, dynamic> permissions) {
    final permissionSet = <Permission>{};

    for (final entry in permissions.entries) {
      if (entry.value == true) {
        // Map permission strings to enum values
        switch (entry.key) {
          case 'manage_organization':
            permissionSet.addAll([Permission.orgRead, Permission.orgWrite, Permission.orgSettings]);
            break;
          case 'manage_users':
            permissionSet.addAll([Permission.userRead, Permission.userWrite, Permission.userInvite, Permission.userRoles]);
            break;
          case 'manage_quests':
            permissionSet.addAll([Permission.questRead, Permission.questWrite, Permission.questCreate, Permission.questAssign]);
            break;
          case 'view_analytics':
            permissionSet.addAll([Permission.analyticsRead, Permission.reportsRead]);
            break;
          case 'view_quests':
            permissionSet.add(Permission.questRead);
            break;
          case 'participate':
            permissionSet.addAll([Permission.questRead, Permission.questWrite]);
            break;
          case 'view_profile':
            permissionSet.add(Permission.userRead);
            break;
          case 'manage_roles':
            permissionSet.add(Permission.userRoles);
            break;
        }
      }
    }

    // Ensure at least basic read permissions
    if (permissionSet.isEmpty) {
      permissionSet.addAll([Permission.orgRead, Permission.userRead, Permission.questRead]);
    }

    return permissionSet;
  }

  /// Login with OAuth provider
  Future<Map<String, dynamic>> loginWithOAuth({
    required String provider,
    required String providerId,
    required String email,
    required String displayName,
    String? avatarUrl,
  }) async {
    try {
      // Check if user exists with this OAuth provider
      final existingUser = await _findUserByOAuthProvider(provider, providerId);

      if (existingUser != null) {
        // User exists, generate tokens and return
        final accessToken = _generateJwtToken({
          'userId': existingUser['id'],
          'email': existingUser['email'],
          'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          'exp': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
        });
        final refreshToken = _generateRefreshToken();

        return {
          'success': true,
          'user': existingUser,
          'accessToken': accessToken,
          'refreshToken': refreshToken,
          'expiresIn': 3600, // 1 hour
        };
      } else {
        // Check if user exists with this email
        final existingEmailUser = await _findUserByEmail(email);

        if (existingEmailUser != null) {
          // Link OAuth account to existing user
          await _linkOAuthAccount(existingEmailUser['id'], provider, providerId);

          final accessToken = _generateJwtToken({
            'userId': existingEmailUser['id'],
            'email': existingEmailUser['email'],
            'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
            'exp': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
          });
          final refreshToken = _generateRefreshToken();

          return {
            'success': true,
            'user': existingEmailUser,
            'accessToken': accessToken,
            'refreshToken': refreshToken,
            'expiresIn': 3600,
          };
        } else {
          // Create new user
          final userId = _uuid.v4();

          await _dbService.execute(
            '''
              INSERT INTO quester.users (id, email, display_name, avatar_url, email_verified, created_at, updated_at)
              VALUES (@userId::uuid, @email, @displayName, @avatarUrl, true, NOW(), NOW())
            ''',
            parameters: {
              'userId': userId,
              'email': email,
              'displayName': displayName,
              'avatarUrl': avatarUrl,
            },
          );

          // Link OAuth account
          await _linkOAuthAccount(userId, provider, providerId);

          final accessToken = _generateJwtToken({
            'userId': userId,
            'email': email,
            'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
            'exp': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
          });
          final refreshToken = _generateRefreshToken();

          LoggingService.info('OAuth user created for user: $userId');

          return {
            'success': true,
            'user': {
              'id': userId,
              'email': email,
              'display_name': displayName,
              'avatar_url': avatarUrl,
              'email_verified': true,
            },
            'accessToken': accessToken,
            'refreshToken': refreshToken,
            'expiresIn': 3600,
          };
        }
      }
    } catch (e) {
      LoggingService.error('OAuth login failed', tag: 'AuthService', error: e);
      throw Exception('OAuth login failed: $e');
    }
  }

  /// Find user by OAuth provider
  Future<Map<String, dynamic>?> _findUserByOAuthProvider(String provider, String providerId) async {
    try {
      final result = await _dbService.execute(
        '''
          SELECT u.id, u.email, u.display_name, u.avatar_url, u.email_verified, u.created_at, u.updated_at
          FROM quester.users u
          JOIN quester.oauth_accounts oa ON u.id = oa.user_id
          WHERE oa.provider = @provider AND oa.provider_id = @providerId
        ''',
        parameters: {
          'provider': provider,
          'providerId': providerId,
        },
      );

      if (result.isEmpty) return null;

      final row = result.first;
      return {
        'id': row[0] as String,
        'email': row[1] as String,
        'display_name': row[2] as String? ?? 'User',
        'avatar_url': row[3] as String?,
        'email_verified': row[4] as bool? ?? false,
        'created_at': row[5] as String,
        'updated_at': row[6] as String,
      };
    } catch (e) {
      LoggingService.error('Error finding user by OAuth provider', tag: 'AuthService', error: e);
      return null;
    }
  }

  /// Link OAuth account to user
  Future<void> _linkOAuthAccount(String userId, String provider, String providerId) async {
    try {
      await _dbService.execute(
        '''
          INSERT INTO quester.oauth_accounts (user_id, provider, provider_id, created_at)
          VALUES (@userId::uuid, @provider, @providerId, NOW())
          ON CONFLICT (user_id, provider) DO UPDATE SET
            provider_id = @providerId,
            updated_at = NOW()
        ''',
        parameters: {
          'userId': userId,
          'provider': provider,
          'providerId': providerId,
        },
      );
    } catch (e) {
      LoggingService.error('Error linking OAuth account', tag: 'AuthService', error: e);
      throw Exception('Failed to link OAuth account');
    }
  }

  /// Resend verification email to user
  Future<void> resendVerificationEmail(String userId) async {
    try {
      // Get user email
      final result = await _dbService.execute(
        'SELECT email FROM quester.users WHERE id = @userId::uuid',
        parameters: {'userId': userId},
      );

      if (result.isEmpty) {
        throw Exception('User not found');
      }

      final email = result.first[0] as String;

      // Generate new verification token
      final verificationToken = _generateSecureToken();

      // Store verification token in database
      await _dbService.execute(
        '''
          INSERT INTO quester.email_verification_tokens (user_id, token, email, expires_at, created_at)
          VALUES (@userId::uuid, @token, @email, @expiresAt::timestamptz, NOW())
          ON CONFLICT (user_id) DO UPDATE SET
            token = @token,
            expires_at = @expiresAt::timestamptz,
            created_at = NOW()
        ''',
        parameters: {
          'userId': userId,
          'token': verificationToken,
          'email': email,
          'expiresAt': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
        },
      );

      LoggingService.email('Verification email resent for $email', recipient: email);
    } catch (e) {
      LoggingService.error('Error resending verification email', tag: 'AuthService', error: e);
      throw Exception('Failed to resend verification email: $e');
    }
  }

  /// Update user profile
  Future<void> updateUserProfile({
    required String userId,
    required Map<String, dynamic> profileData,
  }) async {
    try {
      final updateFields = <String>[];
      final parameters = <String, dynamic>{'userId': userId};

      // Build dynamic update query based on provided fields
      if (profileData.containsKey('displayName')) {
        updateFields.add('display_name = @displayName');
        parameters['displayName'] = profileData['displayName'];
      }

      if (profileData.containsKey('firstName')) {
        updateFields.add('first_name = @firstName');
        parameters['firstName'] = profileData['firstName'];
      }

      if (profileData.containsKey('lastName')) {
        updateFields.add('last_name = @lastName');
        parameters['lastName'] = profileData['lastName'];
      }

      if (profileData.containsKey('avatarUrl')) {
        updateFields.add('avatar_url = @avatarUrl');
        parameters['avatarUrl'] = profileData['avatarUrl'];
      }

      if (updateFields.isEmpty) {
        throw Exception('No valid fields to update');
      }

      // Add updated_at field
      updateFields.add('updated_at = NOW()');

      final query = '''
        UPDATE quester.users
        SET ${updateFields.join(', ')}
        WHERE id = @userId::uuid
      ''';

      await _dbService.execute(query, parameters: parameters);

      LoggingService.info('User profile updated for user: $userId');
    } catch (e) {
      LoggingService.error('Error updating user profile', tag: 'AuthService', error: e);
      throw Exception('Failed to update user profile: $e');
    }
  }

  /// Get all user sessions
  Future<List<Map<String, dynamic>>> getUserSessions(String userId) async {
    try {
      final result = await _dbService.execute(
        '''
          SELECT id, ip_address, user_agent, created_at, last_activity, is_active
          FROM quester.user_sessions_enhanced
          WHERE user_id = @userId::uuid AND is_active = true
          ORDER BY last_activity DESC
        ''',
        parameters: {'userId': userId},
      );

      return result.map((row) => {
        'id': row[0] as String,
        'ipAddress': row[1] as String,
        'userAgent': row[2] as String,
        'createdAt': row[3] as String,
        'lastActivity': row[4] as String,
        'isActive': row[5] as bool,
      }).toList();
    } catch (e) {
      LoggingService.error('Error getting user sessions', tag: 'AuthService', error: e);
      return [];
    }
  }

  /// Terminate all user sessions
  Future<void> terminateAllUserSessions(String userId) async {
    try {
      await _dbService.execute(
        '''
          UPDATE quester.user_sessions_enhanced
          SET is_active = false, updated_at = NOW()
          WHERE user_id = @userId::uuid AND is_active = true
        ''',
        parameters: {'userId': userId},
      );

      LoggingService.info('All sessions terminated for user: $userId');
    } catch (e) {
      LoggingService.error('Error terminating user sessions', tag: 'AuthService', error: e);
      throw Exception('Failed to terminate sessions: $e');
    }
  }

  /// Generate secure token for verification
  String _generateSecureToken() {
    final bytes = List<int>.generate(32, (i) => _random.nextInt(256));
    return base64Url.encode(bytes);
  }

  /// Parse user role from database string
  UserRole _parseUserRole(String? roleString) {
    if (roleString == null) return UserRole.newcomer;

    switch (roleString.toLowerCase()) {
      case 'newcomer':
        return UserRole.newcomer;
      case 'apprentice':
        return UserRole.apprentice;
      case 'journeyman':
        return UserRole.journeyman;
      case 'expert':
        return UserRole.expert;
      case 'master':
        return UserRole.master;
      case 'grandmaster':
        return UserRole.grandmaster;
      case 'legendary':
        return UserRole.legendary;
      case 'mythic':
        return UserRole.mythic;
      default:
        return UserRole.newcomer;
    }
  }

  /// Parse user preferences from JSON string
  Map<String, dynamic> _parseUserPreferences(String? preferencesJson) {
    if (preferencesJson == null || preferencesJson.isEmpty) {
      return {};
    }

    try {
      return jsonDecode(preferencesJson) as Map<String, dynamic>;
    } catch (e) {
      LoggingService.error('Error parsing user preferences', tag: 'AuthService', error: e);
      return {};
    }
  }
}