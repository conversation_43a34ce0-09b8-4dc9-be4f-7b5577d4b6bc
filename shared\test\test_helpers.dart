/// Test helpers for MFA system integration tests
library;
import 'dart:math';

class TestHelpers {
  static final Random _random = Random();

  /// Generate a mock TOTP code from secret
  static String generateMockTOTPCode(String secret) {
    // In real implementation, this would use the TOTP algorithm
    // For testing, we'll generate a 6-digit code based on the secret
    final hash = secret.hashCode.abs();
    return (hash % 1000000).toString().padLeft(6, '0');
  }

  /// Generate a mock TOTP secret
  static String generateMockSecret() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    return List.generate(32, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// Extract verification code from recovery ID (mock implementation)
  static String extractVerificationCode(String recoveryId) {
    // Mock implementation - extract code from recovery ID
    return recoveryId.substring(recoveryId.length - 6);
  }

  /// Create mock recovery result
  static Map<String, dynamic> createMockRecoveryResult(String method, String reason) {
    return {
      'success': true,
      'recovery_id': 'rec_${_random.nextInt(1000000)}',
      'method': method,
      'reason': reason,
      'expires_at': DateTime.now().add(Duration(minutes: 15)).toIso8601String(),
    };
  }

  /// Create mock verification result
  static Map<String, dynamic> createMockVerificationResult(bool success) {
    return {
      'success': success,
      'message': success ? 'Verification successful' : 'Verification failed',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock completion result
  static Map<String, dynamic> createMockCompletionResult(bool success) {
    return {
      'success': success,
      'completed_at': DateTime.now().toIso8601String(),
      'recovery_complete': success,
    };
  }
}

/// Mock test database for integration tests
class TestDatabase {
  late String _connectionString;
  
  TestDatabase._();

  static Future<TestDatabase> create() async {
    final db = TestDatabase._();
    db._connectionString = 'test_db_${Random().nextInt(10000)}';
    // In a real implementation, this would set up a test database
    return db;
  }

  Future<void> cleanup() async {
    // Clean up test database
  }

  String get connectionString => _connectionString;
}
